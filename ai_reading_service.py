#
#  AI Reading Service - AI阅读文件处理服务
#  负责文件转换、解析  API调用、内容摘要生成等
#

import os
import logging
import requests
import zipfile
import tempfile
import subprocess
from datetime import datetime
from typing import Optional, Dict, Any
from dotenv import load_dotenv

import sys
import os
# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# 加载环境变量
load_dotenv(os.path.join(project_root, 'sz', '.env'))

from api.db.db_models import AIReadingFile, AIReadingConversation
from api.utils.file_utils import get_project_base_directory

class AIReadingService:
    """AI阅读服务类"""

    def __init__(self):
        # 从环境变量获取解析 服务器配置
        self.mineru_server_url = os.getenv('MINERU_SERVER_URL', 'http://*************:7860')
        self.mineru_api_key = os.getenv('MINERU_API_KEY', '')
        self.temp_dir = os.path.join(get_project_base_directory(), 'temp', 'ai_reading')

        # Ensure temp directory exists
        os.makedirs(self.temp_dir, exist_ok=True)
        
    def convert_to_pdf(self, file_path: str, file_type: str) -> Optional[str]:
        """
        将Word/PPT文件转换为PDF
        
        Args:
            file_path: 原始文件路径
            file_type: 文件类型 (docx, pptx, doc, ppt)
            
        Returns:
            转换后的PDF文件路径，失败返回None
        """
        try:
            if file_type.lower() == 'pdf':
                return file_path  # 已经是PDF，直接返回
            
            # 生成PDF文件路径
            base_name = os.path.splitext(os.path.basename(file_path))[0]
            pdf_path = os.path.join(self.temp_dir, f"{base_name}.pdf")
            
            # 使用LibreOffice进行转换
            cmd = [
                'libreoffice',
                '--headless',
                '--convert-to', 'pdf',
                '--outdir', self.temp_dir,
                file_path
            ]

            # 设置环境变量以确保LibreOffice能找到所需的库
            env = os.environ.copy()
            env['LD_LIBRARY_PATH'] = '/usr/lib/libreoffice/program:' + env.get('LD_LIBRARY_PATH', '')

            # 执行转换命令
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300, env=env)

            if result.returncode == 0 and os.path.exists(pdf_path):
                # 如果有stderr输出但转换成功，记录为警告而不是错误
                if result.stderr and result.stderr.strip():
                    logging.warning(f"转换过程中的警告信息: {result.stderr}")
                return pdf_path
            else:
                logging.error(f"文件转换失败: {result.stderr}")
                return None
                
        except subprocess.TimeoutExpired:
            logging.error("文件转换超时")
            return None
        except Exception as e:
            logging.exception(f"文件转换异常: {str(e)}")
            return None
    
    def upload_file_to_cdn(self, file_path: str) -> Optional[str]:
        """
        上传文件到本地存储
        由于使用本地解析 服务器，不需要上传到外部CDN
        """
        # 使用本地解析 服务器，直接返回本地文件路径
        if os.path.exists(file_path):
            return file_path
        else:
            logging.error(f"文件不存在: {file_path}")
            return None
    
    def call_mineru_api(self, pdf_path: str) -> Optional[str]:
        """
        调用解析 API进行PDF解析

        Args:
            pdf_path: PDF文件路径

        Returns:
            解析后的Markdown内容，失败返回None
        """
        try:
            from gradio_client import Client, handle_file

            # 连接到配置的Mineru服务器
            logging.info(f"连接到解析 服务器: {self.mineru_server_url}")
            client = Client(self.mineru_server_url)

            # 调用解析 API
            result = client.predict(
                file_path=handle_file(pdf_path),
                end_pages=500,
                is_ocr=False,
                formula_enable=True,
                table_enable=True,
                language="ch",
                backend="pipeline",
                url="http://localhost:30000",
                api_name="/to_markdown"
            )

            # 返回结果是一个包含4个元素的元组
            # [0] str - Markdown 文件内容
            # [1] str - 暂时不处理
            # [2] filepath - 暂时不处理
            # [3] filepath - 暂时不处理

            if result and len(result) > 0:
                markdown_content = result[0]
                logging.info(f"调用解析  API成功返回内容，长度: {len(markdown_content)}")
                return markdown_content
            else:
                logging.error("调用解析  API返回空结果")
                return None

        except Exception as e:
            logging.exception(f"调用解析 API异常: {str(e)}")
            return None
    
    def check_mineru_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        检查Mineru任务状态
        由于使用本地Mineru服务器，直接返回完成状态
        """
        try:
            # 使用本地Mineru服务器，处理是同步的，直接返回完成状态
            return {
                'task_id': task_id,
                'state': 'done',  # pending, running, done, failed
                'result_path': None  # 本地处理不需要下载
            }

        except Exception as e:
            logging.exception(f"查询Mineru状态异常: {str(e)}")
            return None
    
    def download_and_extract_result(self, result_data: str, file_id: str) -> Optional[str]:
        """
        保存Mineru处理结果

        Args:
            result_data: Markdown内容数据
            file_id: 文件ID

        Returns:
            Markdown文件路径，失败返回None
        """
        try:
            # 直接保存Markdown内容到文件
            markdown_path = os.path.join(self.temp_dir, f"{file_id}.md")

            with open(markdown_path, 'w', encoding='utf-8') as f:
                f.write(result_data)

            logging.info(f"Markdown文件已保存: {markdown_path}")
            return markdown_path
            
            # 查找Markdown文件
            for root, dirs, files in os.walk(extract_dir):
                for file in files:
                    if file.endswith('.md'):
                        md_path = os.path.join(root, file)
                        # 移动到标准位置
                        final_md_path = os.path.join(self.temp_dir, f"{file_id}.md")
                        os.rename(md_path, final_md_path)
                        
                        # 清理临时文件
                        os.remove(zip_path)
                        import shutil
                        shutil.rmtree(extract_dir)
                        
                        return final_md_path
            
            logging.error("未找到Markdown文件")
            return None
            
        except Exception as e:
            logging.exception(f"下载解压结果异常: {str(e)}")
            return None
    
    def generate_summary(self, markdown_content: str) -> str:
        """
        生成文档摘要

        Args:
            markdown_content: Markdown内容

        Returns:
            文档摘要
        """
        try:
            if not markdown_content or not markdown_content.strip():
                return "文档内容为空"

            lines = markdown_content.split('\n')

            # 提取标题和重要内容
            titles = []
            content_lines = []

            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # 提取标题
                if line.startswith('#'):
                    title = line.lstrip('#').strip()
                    if title and len(title) > 2:  # 过滤太短的标题
                        titles.append(title)
                # 提取正文内容
                elif not line.startswith('![') and not line.startswith('|') and len(line) > 10:
                    content_lines.append(line)

                # 限制处理的行数，避免处理过长的文档
                if len(titles) + len(content_lines) >= 50:
                    break

            # 构建摘要
            summary_parts = []

            # 添加主要标题
            if titles:
                main_titles = titles[:3]  # 取前3个标题
                summary_parts.append("主要内容：" + "；".join(main_titles))

            # 添加关键内容
            if content_lines:
                key_content = content_lines[:3]  # 取前3段内容
                summary_parts.append("关键信息：" + "；".join(key_content))

            # 组合摘要
            summary = "。".join(summary_parts)

            # 限制摘要长度
            if len(summary) > 500:
                summary = summary[:500] + "..."

            return summary if summary else "无法生成摘要"

        except Exception as e:
            logging.exception(f"生成摘要异常: {str(e)}")
            return "摘要生成失败"
    
    def process_file(self, file_id: str) -> bool:
        """
        处理文件的完整流程
        
        Args:
            file_id: 文件ID
            
        Returns:
            处理是否成功
        """
        try:
            # 获取文件记录
            ai_file = AIReadingFile.get(AIReadingFile.id == file_id)
            
            # 1. 转换为PDF（如果需要）
            ai_file.processing_status = 'converting_to_pdf'
            ai_file.processing_progress = 20.0
            ai_file.processing_message = '正在转换为PDF格式'
            ai_file.save()
            
            pdf_path = self.convert_to_pdf(ai_file.original_file_path, ai_file.file_type)
            if not pdf_path:
                ai_file.processing_status = 'failed'
                ai_file.processing_message = 'PDF转换失败'
                ai_file.save()
                return False
            
            ai_file.pdf_file_path = pdf_path
            ai_file.save()
            
            # 2. 调用解析 API
            ai_file.processing_status = 'calling_mineru'
            ai_file.processing_progress = 40.0
            ai_file.processing_message = '正在调用解析 API解析文档'
            ai_file.save()

            markdown_content = self.call_mineru_api(pdf_path)
            if not markdown_content:
                ai_file.processing_status = 'failed'
                ai_file.processing_message = '解析  API调用失败'
                ai_file.save()
                return False

            # 3. 保存Markdown内容
            ai_file.processing_status = 'processing'
            ai_file.processing_progress = 80.0
            ai_file.processing_message = '正在保存文档内容'
            ai_file.save()

            # 保存Markdown文件
            markdown_path = self.download_and_extract_result(markdown_content, file_id)
            if not markdown_path:
                ai_file.processing_status = 'failed'
                ai_file.processing_message = 'Markdown文件保存失败'
                ai_file.save()
                return False

            # 4. 生成摘要
            ai_file.processing_status = 'generating_summary'
            ai_file.processing_progress = 90.0
            ai_file.processing_message = '正在生成文档摘要'
            ai_file.save()

            summary = self.generate_summary(markdown_content)

            # 5. 完成处理
            ai_file.processing_status = 'completed'
            ai_file.processing_progress = 100.0
            ai_file.processing_message = '文档处理完成'
            ai_file.markdown_file_path = markdown_path
            ai_file.content_summary = summary
            ai_file.summary_generated_at = datetime.now()
            ai_file.save()

            logging.info(f"文件处理完成: {file_id}, Markdown路径: {markdown_path}")
            return True
            
        except Exception as e:
            logging.exception(f"处理文件异常: {str(e)}")
            try:
                ai_file = AIReadingFile.get(AIReadingFile.id == file_id)
                ai_file.processing_status = 'failed'
                ai_file.processing_message = f'处理失败: {str(e)}'
                ai_file.save()
            except:
                pass
            return False
