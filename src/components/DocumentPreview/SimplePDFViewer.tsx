import React, { useState, useEffect } from 'react';
import { Spin, Alert, Button, Typography } from 'antd';
import request from '@/utils/request';

const { Text } = Typography;

interface SimplePDFViewerProps {
  file?: File | string;
  className?: string;
}

const SimplePDFViewer: React.FC<SimplePDFViewerProps> = ({ file, className }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');
  const [pdfUrl, setPdfUrl] = useState<string>('');

  useEffect(() => {
    if (!file) {
      setError('No PDF file provided');
      setLoading(false);
      return;
    }

    const loadPDF = async () => {
      try {
        setLoading(true);
        setError('');
        console.log('Loading PDF with SimplePDFViewer, file type:', typeof file);

        if (typeof file === 'string') {
          // 对于URL字符串，使用request获取PDF数据以携带认证头
          console.log('Fetching PDF from URL with authentication:', file);
          try {
            const response = await request(file, {
              method: 'GET',
              responseType: 'blob',
              getResponse: true,
            });

            if (response.response.ok) {
              // 创建blob URL
              const blob = response.data;
              const url = URL.createObjectURL(blob);
              setPdfUrl(url);
              console.log('PDF blob URL created with authentication:', url);
            } else {
              throw new Error(`Failed to fetch PDF: ${response.response.status} ${response.response.statusText}`);
            }
          } catch (fetchError) {
            console.error('Authenticated fetch failed:', fetchError);
            throw fetchError;
          }
        } else if (file instanceof File) {
          // 如果是File对象，创建URL
          const url = URL.createObjectURL(file);
          setPdfUrl(url);
          console.log('PDF blob URL created:', url);
        } else {
          throw new Error('Invalid file type');
        }

        setLoading(false);
      } catch (err) {
        console.error('Error loading PDF:', err);
        setError(err instanceof Error ? err.message : 'Failed to load PDF');
        setLoading(false);
      }
    };

    loadPDF();

    return () => {
      // 清理blob URL
      if (pdfUrl) {
        URL.revokeObjectURL(pdfUrl);
      }
    };
  }, [file]);

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '40px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>
          <Text type="secondary">Loading PDF...</Text>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert
        message="PDF Preview Error"
        description={error}
        type="error"
        showIcon
      />
    );
  }

  if (!pdfUrl) {
    return (
      <Alert
        message="PDF Not Available"
        description="Unable to load PDF file for preview"
        type="warning"
        showIcon
      />
    );
  }

  return (
    <div className={className}>
      <div style={{ 
        width: '100%', 
        height: '600px', 
        border: '1px solid #d9d9d9', 
        borderRadius: '6px',
        overflow: 'hidden',
        backgroundColor: '#f5f5f5'
      }}>
        {/* 使用embed标签，比iframe更可靠 */}
        <embed
          src={pdfUrl}
          type="application/pdf"
          width="100%"
          height="100%"
          style={{ 
            border: 'none',
            display: 'block'
          }}
        />
        
        {/* 如果embed不支持，显示备用内容 */}
        <div style={{ 
          padding: '20px', 
          textAlign: 'center',
          color: '#666',
          display: 'none' // 只有当embed失败时才显示
        }}>
          <p>您的浏览器不支持PDF预览</p>
          <Button 
            type="primary" 
            href={pdfUrl} 
            target="_blank"
            rel="noopener noreferrer"
          >
            在新窗口中打开PDF
          </Button>
        </div>
      </div>
    </div>
  );
};

export default SimplePDFViewer;
