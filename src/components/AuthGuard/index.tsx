import { useAuth } from '@/hooks/auth-hooks';
import Loading from '@/components/Loading';
import { useEffect } from 'react';
import { useNavigate } from 'umi';

interface AuthGuardProps {
  children: React.ReactNode;
  requireAuth?: boolean; // 是否需要认证，默认true
  redirectTo?: string; // 重定向地址，默认'/login'
}

const AuthGuard: React.FC<AuthGuardProps> = ({ 
  children, 
  requireAuth = true, 
  redirectTo = '/login' 
}) => {
  const { isLogin } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    if (requireAuth && isLogin === false) {
      // 需要认证但未登录，跳转到登录页
      navigate(redirectTo);
    } else if (!requireAuth && isLogin === true) {
      // 不需要认证但已登录（如登录页），跳转到Dashboard
      const redirectPath = sessionStorage.getItem('redirectAfterLogin') || '/dashboard';
      sessionStorage.removeItem('redirectAfterLogin');
      navigate(redirectPath);
    }
  }, [isLogin, navigate, requireAuth, redirectTo]);

  // 如果还在检查认证状态，显示加载
  if (isLogin === null) {
    return <Loading tip="Checking authentication..." />;
  }

  // 如果需要认证但未登录，显示加载（即将跳转）
  if (requireAuth && isLogin === false) {
    return <Loading tip="Redirecting to login..." />;
  }

  // 如果不需要认证但已登录，显示加载（即将跳转）
  if (!requireAuth && isLogin === true) {
    return <Loading tip="Redirecting to dashboard..." />;
  }

  return <>{children}</>;
};

export default AuthGuard;
