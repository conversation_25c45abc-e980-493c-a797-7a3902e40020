import React from 'react';
import { useQuery } from '@tanstack/react-query';
import llmService, { ILLMListResponse, ILLMFactory, IMyLLMResponse } from '@/services/llm-service';

// 获取所有模型列表
export const useLLMList = (modelType?: string) => {
  return useQuery<{ data: ILLMListResponse }>({
    queryKey: ['llm-list', modelType],
    queryFn: () => llmService.getModelList(modelType),
    staleTime: 5 * 60 * 1000, // 5分钟缓存
  });
};

// 获取LLM工厂列表
export const useLLMFactories = () => {
  return useQuery<{ data: ILLMFactory[] }>({
    queryKey: ['llm-factories'],
    queryFn: () => llmService.getFactories(),
    staleTime: 10 * 60 * 1000, // 10分钟缓存
  });
};

// 获取我的LLM模型
export const useMyLLMs = () => {
  return useQuery<{ data: IMyLLMResponse }>({
    queryKey: ['my-llms'],
    queryFn: () => llmService.getMyModels(),
    staleTime: 2 * 60 * 1000, // 2分钟缓存
  });
};

// 获取embedding模型列表
export const useEmbeddingModels = () => {
  return useQuery<{ data: ILLMListResponse }>({
    queryKey: ['embedding-models'],
    queryFn: () => llmService.getEmbeddingModels(),
    staleTime: 5 * 60 * 1000,
  });
};

// 获取rerank模型列表
export const useRerankModels = () => {
  return useQuery<{ data: ILLMListResponse }>({
    queryKey: ['rerank-models'],
    queryFn: () => llmService.getRerankModels(),
    staleTime: 5 * 60 * 1000,
  });
};

// 获取chat模型列表
export const useChatModels = () => {
  return useQuery<{ data: ILLMListResponse }>({
    queryKey: ['chat-models'],
    queryFn: () => llmService.getChatModels(),
    staleTime: 5 * 60 * 1000,
  });
};

// 处理模型列表数据的工具函数
export const useProcessedModels = (modelType: 'embedding' | 'rerank' | 'chat') => {
  const { data, isLoading, error } = useLLMList(modelType);
  
  const processedModels = React.useMemo(() => {
    if (!data?.data) return [];
    
    const models: Array<{ value: string; label: string; factory: string; available: boolean }> = [];
    
    Object.entries(data.data).forEach(([factory, factoryModels]) => {
      factoryModels.forEach(model => {
        if (model.model_type.includes(modelType) && model.available) {
          models.push({
            value: model.llm_name,
            label: `${model.llm_name} (${factory})`,
            factory: factory,
            available: model.available,
          });
        }
      });
    });
    
    return models.sort((a, b) => a.label.localeCompare(b.label));
  }, [data, modelType]);
  
  return {
    models: processedModels,
    isLoading,
    error,
  };
};

// 获取可用的embedding模型选项
export const useEmbeddingModelOptions = () => {
  return useProcessedModels('embedding');
};

// 获取可用的rerank模型选项
export const useRerankModelOptions = () => {
  return useProcessedModels('rerank');
};

// 获取可用的chat模型选项
export const useChatModelOptions = () => {
  return useProcessedModels('chat');
};

// 获取parser类型选项（从后端获取或使用预定义列表）
export const useParserOptions = () => {
  return React.useMemo(() => [
    { value: 'naive', label: 'General' },
    { value: 'qa', label: 'Q&A' },
    { value: 'resume', label: 'Resume' },
    { value: 'manual', label: 'Manual' },
    { value: 'table', label: 'Table' },
    { value: 'paper', label: 'Paper' },
    { value: 'book', label: 'Book' },
    { value: 'laws', label: 'Laws' },
    { value: 'presentation', label: 'Presentation' },
    { value: 'picture', label: 'Picture' },
    { value: 'one', label: 'One' },
    { value: 'audio', label: 'Audio' },
    { value: 'email', label: 'Email' },
    { value: 'tag', label: 'Tag' },
    { value: 'knowledge_graph', label: 'Knowledge Graph' },
  ], []);
};

// 获取语言选项
export const useLanguageOptions = () => {
  return React.useMemo(() => [
    { value: 'English', label: 'English' },
    { value: 'Chinese', label: '中文' },
    { value: 'Japanese', label: '日本語' },
    { value: 'Korean', label: '한국어' },
    { value: 'French', label: 'Français' },
    { value: 'German', label: 'Deutsch' },
    { value: 'Spanish', label: 'Español' },
    { value: 'Portuguese', label: 'Português' },
    { value: 'Russian', label: 'Русский' },
    { value: 'Italian', label: 'Italiano' },
    { value: 'Arabic', label: 'العربية' },
    { value: 'Hindi', label: 'हिन्दी' },
  ], []);
};

// 获取LLM模型选项用于Dialog配置 - 只返回chat类型的模型
export const useLLMOptions = () => {
  const { data: myLLMsResponse, isLoading } = useMyLLMs();

  const models: Array<{ label: string; value: string; model_type: string }> = [];

  if (myLLMsResponse?.data) {
    // 遍历所有工厂，只筛选chat类型的模型
    Object.entries(myLLMsResponse.data).forEach(([factoryName, factory]: [string, any]) => {
      if (factory.llm && Array.isArray(factory.llm)) {
        factory.llm.forEach((llm: any) => {
          // 只添加chat类型的模型
          if (llm.type === 'chat') {
            models.push({
              label: `${llm.name} (${llm.type}) - ${factoryName}`,
              value: `${llm.name}@${factoryName}`,
              model_type: llm.type,
            });
          }
        });
      }
    });
  }

  return { models, isLoading };
};

// 获取Rerank模型选项
export const useRerankOptions = () => {
  const { data: myLLMsResponse, isLoading } = useMyLLMs();

  const rerankModels: Array<{ label: string; value: string }> = [];

  if (myLLMsResponse?.data) {
    // 遍历所有工厂，筛选rerank类型的模型
    Object.entries(myLLMsResponse.data).forEach(([factoryName, factory]: [string, any]) => {
      if (factory.llm && Array.isArray(factory.llm)) {
        factory.llm.forEach((llm: any) => {
          if (llm.type === 'rerank') {
            rerankModels.push({
              label: `${llm.name} - ${factoryName}`,
              value: `${llm.name}@${factoryName}`,
            });
          }
        });
      }
    });
  }

  return { rerankModels, isLoading };
};
