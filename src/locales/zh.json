{"common": {"confirm": "确认", "cancel": "取消", "delete": "删除", "edit": "编辑", "save": "保存", "create": "创建", "update": "更新", "search": "搜索", "loading": "加载中...", "submit": "提交", "reset": "重置", "back": "返回", "next": "下一步", "previous": "上一步", "close": "关闭", "ok": "确定", "yes": "是", "no": "否", "add": "添加", "remove": "移除", "upload": "上传", "download": "下载", "copy": "复制", "paste": "粘贴", "cut": "剪切", "select": "选择", "selectAll": "全选", "clear": "清空", "refresh": "刷新", "retry": "重试", "view": "查看", "preview": "预览", "settings": "设置", "help": "帮助", "about": "关于", "language": "语言", "comingSoon": "即将推出", "user": "用户", "actions": "操作", "error": "错误", "of": "的", "status": "状态", "created": "创建时间", "more": "更多", "on": "开", "off": "关", "show": "显示", "hide": "隐藏", "unknown": "未知", "noDescription": "无描述", "auto": "自动", "manual": "手动", "bytes": "字节", "items": "项", "total": "总计", "page": "页", "pages": "页", "item": "项", "selected": "已选择", "all": "全部", "none": "无"}, "auth": {"signIn": "登录", "signUp": "注册", "signOut": "退出登录", "email": "邮箱", "password": "密码", "nickname": "昵称", "rememberMe": "记住我", "createAccount": "创建账户", "welcomeBack": "欢迎回到汉邦高科知识库系统", "createAccountDesc": "创建您的账户以开始使用", "dontHaveAccount": "还没有账户？", "alreadyHaveAccount": "已有账户？", "enterEmail": "请输入邮箱", "enterPassword": "请输入密码", "enterNickname": "请输入昵称", "emailRequired": "请输入邮箱！", "passwordRequired": "请输入密码！", "nicknameRequired": "请输入昵称！", "emailInvalid": "请输入有效的邮箱地址！", "orContinueWith": "或者继续使用", "signInWith": "使用{{provider}}登录"}, "navigation": {"dashboard": "仪表板", "knowledgeBase": "知识库设置", "aiRead": "AI阅读", "textToImage": "AI文生图", "documents": "知识库文档", "conversations": "知识库对话", "aiModels": "AI模型设置", "settings": "个人设置", "dialogs": "知识库对话配置", "chat": "聊天", "chatbot": "AI助手"}, "dashboard": {"title": "仪表板", "welcome": "欢迎使用汉邦高科", "overview": "概览", "statistics": "统计", "recentActivity": "最近活动", "welcomeBack": "欢迎回来，{{name}}！", "manageDescription": "管理您的知识库并与AI驱动的洞察互动", "knowledgeBases": "知识库", "quickActions": "快速操作", "recentActivities": "最近活动", "createKnowledgeBase": "创建知识库", "createKnowledgeBaseDesc": "开始构建您的知识仓库", "manageDocuments": "管理文档", "manageDocumentsDesc": "上传和整理您的文档", "startConversation": "开始对话", "startConversationDesc": "与您的AI助手聊天", "configureModels": "配置模型", "configureModelsDesc": "设置您的AI模型", "knowledgeBaseCreated": "知识库已创建", "documentUploaded": "文档已上传", "conversationCompleted": "与AI的对话已完成", "modelConfigUpdated": "模型配置已更新", "activityTypes": {"create": "创建", "upload": "上传", "chat": "聊天", "config": "配置"}}, "knowledge": {"title": "知识库", "createKnowledge": "创建知识库", "knowledgeName": "知识库名称", "description": "描述", "enterName": "请输入知识库名称", "enterDescription": "请输入描述（可选）", "nameRequired": "请输入知识库名称", "createSuccess": "知识库创建成功！", "deleteConfirm": "确定要删除\"{{name}}\"吗？此操作无法撤销。", "deleteSuccess": "知识库和相关对话删除成功！", "dataset": "数据集", "setting": "设置", "retrievalTesting": "检索测试", "chunks": "分块", "createFailed": "创建知识库失败", "searchPlaceholder": "搜索知识库...", "createFirstKnowledge": "创建您的第一个知识库", "searchAllPlaceholder": "搜索知识库、文档...", "deleteTitle": "删除知识库", "totalKnowledgeBases": "知识库总数", "noKnowledgeBases": "未找到知识库", "noMoreKnowledgeBases": "没有更多知识库可加载", "viewDocuments": "查看文档", "manageDescription": "管理您的知识库和文档", "totalDocuments": "总文档数", "totalChunks": "总分块数", "documents": "文档", "noDescription": "暂无描述", "docsCount": "文档", "chunksCount": "块", "loadingSettings": "正在加载知识库设置...", "settingsTitle": "知识库设置", "settingsDescription": "配置您的知识库属性和处理设置", "generalSettings": "基本设置", "knowledgeBaseName": "知识库名称", "nameMaxLength": "名称不能超过100个字符", "namePlaceholder": "请输入知识库名称", "language": "语言", "languageRequired": "请选择语言", "languagePlaceholder": "选择语言", "permission": "权限", "permissionRequired": "请选择权限", "permissionPlaceholder": "选择权限", "permissionPrivate": "私有（仅自己）", "permissionTeam": "团队", "permissionPublic": "公开", "pdfParser": "PDF解析器", "pdfParserRequired": "请选择PDF解析器", "pdfParserPlaceholder": "选择PDF解析器", "parserType": "解析器类型", "parserTypeRequired": "请选择解析器类型", "parserTypePlaceholder": "选择解析器类型", "selectLanguage": "选择语言", "descriptionMaxLength": "描述不能超过500个字符", "descriptionPlaceholder": "请输入描述（可选）", "processingSettings": "处理设置", "embedModel": "嵌入模型", "embedModelRequired": "请选择嵌入模型", "embedModelTooltip": "知识库采用的默认嵌入模型。 一旦知识库内已经产生了文本块后，你将无法更改默认的嵌入模型，除非删除知识库内的所有文本块。", "selectModel": "选择模型", "noModelsAvailable": "没有可用的模型", "dangerZone": "危险区域", "deleteKnowledgeBase": "删除知识库", "deleteWarning": "一旦删除知识库，将无法恢复。请谨慎操作。", "updateSuccess": "知识库更新成功！", "updateFailed": "更新知识库失败", "deleteConfirmMessage": "确定要删除这个知识库吗？", "deleteCannotUndo": "此操作无法撤销。", "deleteAllData": "所有文档和数据将被永久删除。", "deleteFailed": "删除知识库失败", "parserDescriptions": {"naive": {"title": "通用", "description": " 支持的文件格式：MD、MDX、DOCX、XLSX、XLS (Excel 97-2003)、PPT、PDF、TXT、JPEG、JPG、PNG、TIF、GIF、CSV、JSON、EML、HTML。\\n\\n此方法使用\\\"朴素\\\"方法对文件进行分块：\\n• 使用视觉检测模型将文本分割成较小的片段。\\n• 然后，合并相邻片段，直到令牌数超过\\\"文本块令牌数\\\"指定的阈值，此时创建一个块。"}, "qa": {"title": "问答", "description": "此分块方法支持XLSX和CSV/TXT文件格式。\\n\\n• 如果文件是XLSX或XLS (Excel 97-2003)格式，应包含两列且无标题：一列用于问题，另一列用于答案，问题列在答案列之前。可接受多个工作表，前提是列结构正确。\\n• 如果文件是CSV/TXT格式，必须是UTF-8编码，使用TAB作为分隔符来分隔问题和答案。"}, "resume": {"title": "简历", "description": "仅支持PDF格式。\\n\\n我们假设简历具有分层的章节结构，使用最低级别的章节标题作为文档分块的基本单位。因此，同一章节中的图表和表格不会被分离，这可能导致较大的块大小。"}, "manual": {"title": "手册", "description": "仅支持PDF格式。\\n\\n我们假设手册具有分层的章节结构，使用最低级别的章节标题作为文档分块的基本单位。因此，同一章节中的图表和表格不会被分离，这可能导致较大的块大小。"}, "table": {"title": "表格", "description": "支持的文件格式：XLSX、XLS (Excel 97-2003)、CSV。\\n\\n此方法专为表格数据设计。表格中的每一行都被视为一个单独的块，保留数据内的结构和关系。"}, "paper": {"title": "论文", "description": "仅支持PDF文件。\\n\\n论文将按章节分割，如摘要、1.1、1.2等。这种方法使LLM能够更有效地总结论文并提供更全面、易懂的回答。但是，这也会增加AI对话的上下文并增加LLM的计算成本。因此在对话过程中，请考虑减少'topN'的值。"}, "book": {"title": "书籍", "description": "仅支持PDF格式。\\n\\n书籍将按章节或部分分割。此方法针对具有分层结构的长篇内容进行了优化，确保相关内容保持在一起，同时保持合理的块大小。"}, "laws": {"title": "法律", "description": "支持的文件格式：PDF、DOCX、TXT。\\n\\n法律文档将按条款、章节或条文分割。此方法保留法律结构，确保相关法律条款保持在一起以便更好地理解上下文。"}, "presentation": {"title": "演示文稿", "description": "支持的文件格式：PDF、PPTX。\\n\\n幻灯片中的每一页都被视为一个块，并存储其缩略图。此分块方法自动应用于所有上传的PPT文件，因此您无需手动指定。"}, "picture": {"title": "图片", "description": "支持的文件格式：JPEG、JPG、PNG、TIF、GIF、PDF。\\n\\n图片将使用视觉模型处理以提取文本和视觉信息。每个图片都被视为一个单独的块，同时保留视觉和文本内容。"}, "one": {"title": "单一", "description": "支持的文件格式：MD、DOCX、PDF、TXT。\\n\\n整个文档被视为单个块。适用于需要LLM总结整个文档的情况，前提是它能够处理这么多的上下文长度。"}, "audio": {"title": "音频", "description": "支持的文件格式：MP3、WAV、M4A、AAC。\\n\\n音频文件将使用语音转文本模型转录为文本，然后根据自然语音停顿和内容边界进行分块。"}, "email": {"title": "邮件", "description": "支持的文件格式：EML、MSG。\\n\\n邮件消息将被解析以提取标题、正文内容和附件。每封邮件通常被视为一个单独的块，同时保留邮件结构和元数据。"}, "tag": {"title": "标签", "description": "使用'标签'分块方法的知识库充当标签集。其他知识库可以使用它来标记自己的块，对这些知识库的查询也将使用此标签集进行标记。\\n\\n使用'标签'作为分块方法的知识库不会参与检索增强生成(RAG)过程。此知识库中的每个块都是独立的描述-标签对。\\n\\n支持的文件格式包括XLSX和CSV/TXT：\\n• 如果文件是XLSX格式，应包含两列且无标题：一列用于标签描述，另一列用于标签名称，描述列在标签列之前。\\n• 如果文件是CSV/TXT格式，必须是UTF-8编码，使用TAB作为分隔符来分隔描述和标签。\\n• 在标签列中，使用逗号分隔标签。"}, "knowledge_graph": {"title": "知识图谱", "description": "支持的文件格式：DOCX、EXCEL、PPT、IMAGE、PDF、TXT、MD、JSON、EML。\\n\\n此方法使用'朴素'/'通用'方法对文件进行分块。它将文档分割成段落，然后合并相邻段落，直到令牌数超过'文本块令牌数'指定的阈值，此时创建一个块。\\n\\n然后将块输入到LLM中，为知识图谱和思维导图提取实体和关系。确保设置实体类型。"}}, "backToKnowledgeBase": "返回知识库", "created": "创建时间", "manageDocuments": "管理您知识库中的文档", "searchDocuments": "搜索文档...", "batchParse": "批量解析", "batchDelete": "批量删除", "refresh": "刷新", "webCrawl": "网页爬取", "uploadDocument": "上传文档", "completed": "已完成", "processing": "处理中", "systemHealthWarning": "系统健康警告", "systemHealthDescription": "由于系统健康问题，自动刷新已被禁用。请在设置中检查系统状态。", "documentsRange": "{{start}}-{{end}} 共 {{total}} 个文档", "clickOrDragToUpload": "点击或拖拽文件到此区域上传", "supportedFormats": "支持 PDF、DOC、DOCX、TXT 等文档格式", "documentName": "文档名称", "url": "网址", "enterDocumentName": "输入文档名称", "enterNewDocumentName": "输入新文档名称", "parserConfiguration": "解析器配置 (JSON)", "enterParserConfig": "输入JSON格式的解析器配置", "previewDocument": "预览: {{name}}", "autoRefreshTooltip": "解析期间{{status}}自动刷新", "enableAutoRefresh": "启用", "disableAutoRefresh": "禁用", "webCrawlStarted": "网页爬取开始成功！", "webCrawlFailed": "启动网页爬取失败", "statusUpdated": "文档状态更新成功！", "statusUpdateFailed": "更新文档状态失败", "parsingUpdated": "文档解析更新成功！", "parsingUpdateFailed": "更新文档解析失败", "documentRenamed": "文档重命名成功！", "renameFailed": "重命名文档失败", "parserChanged": "解析器更改成功！", "parserChangeFailed": "更改解析器失败", "uploadSuccess": "文档上传成功！", "uploadFailed": "上传文档失败", "selectDocumentsToParse": "请选择要解析的文档", "selectDocumentsToDelete": "请选择要删除的文档", "deleteDocuments": "删除文档", "deleteDocumentsConfirm": "确定要删除 {{count}} 个文档吗？", "deleteDocumentConfirm": "确定要删除\"{{name}}\"吗？此操作无法撤销。", "deleteDocumentSuccess": "文档删除成功！", "deleteDocumentFailed": "删除文档失败", "reparseWarning": "重新解析将删除现有分块。继续？", "startParsing": "开始解析？", "rename": "重命名", "changeParser": "更改解析器", "preview": "预览", "download": "下载", "actions": "操作", "processBegin": "处理开始", "duration": "持续时间", "progress": "进度", "message": "消息", "type": "类型", "size": "大小", "status": "状态", "parsingStatus": "解析状态", "tokens": "令牌", "parser": "解析器", "thumbnail": "缩略图", "pleaseInputDocumentName": "请输入文档名称！", "pleaseInputURL": "请输入网址！", "pleaseInputValidURL": "请输入有效的网址！", "pleaseSelectParserType": "请选择解析器类型！", "general": "通用", "qa": "问答", "resume": "简历", "manual": "手册", "table": "表格", "paper": "论文", "book": "书籍", "laws": "法律", "presentation": "演示文稿", "picture": "图片", "one": "单一", "audio": "音频", "email": "邮件", "tag": "标签", "knowledgeGraph": "知识图谱", "chunkTokens": "分块令牌数", "chunkTokensRequired": "请输入分块令牌数", "chunkTokensRange": "必须在1到2048之间", "chunkTokensTooltip": "建议的生成文本块的 token 数阈值。如果切分得到的小文本段 token 数达不到这一阈值就会不断与之后的文本段合并，直至再合并下一个文本段会超过这一阈值为止，此时产生一个最终文本块。如果系统在切分文本段时始终没有遇到文本分段标识符，即便文本段 token 数已经超过这一阈值，系统也不会生成新文本块。", "delimiter": "分隔符", "delimiterRequired": "请输入分隔符", "delimiterTooltip": "支持多字符作为分隔符，多字符用两个反引号 \\`\\` 分隔符包裹。若配置成：\n`##`; 系统将首先使用换行符、两个#号以及分号先对文本进行分割，随后再对分得的小文本块按照「建议文本块大小」设定的大小进行拼装。在设置文本分段标识符前请确保理解上述文本分段切片机制。", "autoKeywords": "自动关键词", "autoKeywordsRange": "必须在0到10之间", "autoKeywordsTooltip": "自动提取的关键词数量", "autoQuestions": "自动问题", "autoQuestionsRange": "必须在0到10之间", "autoQuestionsTooltip": "自动生成的问题数量", "html4excel": "HTML4Excel", "html4excelTooltip": "将Excel文件转换为HTML格式以便更好地解析", "layoutRecognition": "布局识别", "layoutRecognitionTooltip": "启用布局识别以便更好地解析文档", "pageSize": "页面大小", "pageSizeRange": "必须在1到50之间", "pageSizeTooltip": "每个任务处理的页面数量", "raptor": "RAPTOR", "raptorTooltip": "启用RAPTOR分层聚类以便更好地检索", "maxClusters": "最大聚类数", "maxClustersRange": "必须在1到256之间", "maxClustersTooltip": "RAPTOR的最大聚类数量", "threshold": "阈值", "thresholdRange": "必须在0到1之间", "thresholdTooltip": "RAPTOR聚类的相似度阈值", "maxTokens": "最大令牌数", "maxTokensRange": "必须在512到8192之间", "maxTokensTooltip": "RAPTOR处理的最大令牌数量", "randomSeed": "随机种子", "randomSeedRange": "必须在0到999999之间", "randomSeedTooltip": "RAPTOR聚类可重现性的随机种子", "parserMethodTitle": "{{title}} 解析器方法", "referenceNotAvailable": "引用内容不可用", "referenceDataNotFound": "{{index}} 对应的引用数据未找到"}, "retrieval": {"testQuestion": "测试问题", "retrievalSettings": "检索设置", "questionPlaceholder": "输入您的问题来测试检索...", "testRetrieval": "测试检索", "enterQuestion": "请输入问题", "foundChunks": "找到 {{count}} 个相关分块（总计：{{total}}）", "testFailed": "检索测试失败", "score": "得分", "keywords": "关键词", "questions": "问题", "results": "结果", "highlightOn": "高亮开启", "totalResults": "总计 {{total}} 个结果", "noResults": "暂无结果。输入问题并点击\"测试检索\"开始。", "configureParameters": "配置检索参数", "adjustParameters": "调整这些参数以针对您的特定用例优化检索性能。", "similarityThreshold": "相似度阈值", "similarityThresholdTooltip": "结果的最小相似度分数", "vectorSimilarityWeight": "向量相似度权重", "vectorSimilarityWeightTooltip": "混合搜索中向量相似度的权重", "topKResults": "Top K 结果", "topKResultsTooltip": "返回的最大结果数量", "rerankModel": "重排序模型", "rerankModelTooltip": "选择重排序模型进行结果重新排序", "selectRerankModel": "选择重排序模型", "noModelsAvailable": "没有可用的模型", "useKnowledgeGraph": "使用知识图谱", "useKnowledgeGraphTooltip": "启用知识图谱以增强检索", "highlightKeywords": "高亮关键词", "highlightKeywordsTooltip": "在结果中高亮匹配的关键词"}, "chunks": {"chunkIdRequired": "分块ID是必需的", "statusUpdated": "分块状态更新成功", "statusUpdateFailed": "更新分块状态失败", "chunkIdMissing": "分块ID缺失", "content": "内容", "keywords": "关键词", "status": "状态", "idMissing": "ID缺失", "enabled": "启用", "disabled": "停用", "idsRequired": "需要知识库ID和文档ID", "backToDocuments": "返回文档", "documentChunks": "文档分块", "viewAndManage": "查看和管理从文档中提取的分块", "totalChunks": "总分块数", "searchPlaceholder": "搜索分块...", "chunksRange": "{{start}}-{{end}} 共 {{total}} 个分块"}, "dialog": {"title": "对话配置", "createDialog": "创建对话", "dialogName": "对话名称", "language": "语言", "description": "描述", "enterDialogName": "请输入对话名称", "selectLanguage": "选择语言", "enterDescription": "请输入描述（可选）", "nameRequired": "请输入对话名称", "languageRequired": "请选择语言", "descriptionMaxLength": "描述不能超过500个字符", "createSuccess": "对话创建成功", "deleteConfirm": "确定要删除这个对话吗？", "deleteSuccess": "对话删除成功", "assistantSettings": "助手设置", "promptConfiguration": "提示词配置", "modelSettings": "模型设置", "assistantName": "助手名称", "enterAssistantName": "请输入助手名称", "assistantNameRequired": "请输入助手名称", "systemPrompt": "系统提示词", "systemPromptTooltip": "定义助手行为的系统指令", "enterSystemPrompt": "输入系统提示...", "prologue": "开场白", "prologueTooltip": "向用户显示的开场消息", "enterPrologue": "请输入开场白消息（可选）", "emptyResponse": "空回复", "emptyResponseTooltip": "当找不到相关信息时的回复", "emptyResponsePlaceholder": "抱歉，我不知道。", "setOpener": "设置开场白", "setOpenerTooltip": "向用户显示的开场消息", "defaultOpener": "您好！我是您的助手，有什么可以帮助您的吗？", "basicInformation": "基本信息", "selectKnowledgeBases": "选择知识库", "selectKnowledgeBasesTooltip": "选择此对话要使用的知识库", "editDialog": "编辑对话", "startChat": "开始聊天", "manageDescription": "管理您的聊天对话和配置", "searchPlaceholder": "搜索对话...", "createFirstDialog": "创建您的第一个对话", "selectDialogsToDelete": "请选择要删除的对话", "deleteDialogs": "删除对话", "batchDeleteConfirm": "确定要删除 {{count}} 个对话吗？", "knowledgeBases": "知识库", "noKnowledgeBases": "无知识库", "llmModel": "LLM模型", "notSet": "未设置", "invalidDialogId": "无效的对话ID", "noDialogIdProvided": "URL中未提供对话ID", "goToDialogs": "前往对话", "assistantAvatar": "助手头像", "quote": "引用", "quoteTooltip": "在回复中启用引用", "keyword": "关键词", "keywordTooltip": "启用关键词提取", "tts": "语音合成", "ttsTooltip": "启用文本转语音", "knowledgeBasesTooltip": "选择此对话要使用的知识库", "upload": "上传", "enterVariableKey": "输入变量键", "optional": "可选", "variable": "变量", "add": "添加", "promptEngine": "提示引擎", "system": "系统", "systemPromptRequired": "系统提示是必需的", "similarityThreshold": "相似度阈值", "similarityThresholdTooltip": "检索结果的最小相似度分数", "vectorSimilarityWeight": "向量相似度权重", "vectorSimilarityWeightTooltip": "混合搜索中向量相似度的权重", "topN": "Top N", "topNTooltip": "要检索的文档数量", "multiTurn": "多轮对话", "multiTurnTooltip": "启用多轮对话", "reasoning": "推理", "reasoningTooltip": "启用推理模式", "rerankModel": "重排序模型", "rerankModelTooltip": "选择用于结果重新排序的重排序模型", "selectRerankModel": "选择重排序模型", "topK": "Top K", "topKTooltip": "要重新排序的文档数量", "variableTooltip": "为提示定义自定义变量", "defaultSystemPrompt": "您是一个智能助手。请根据您的知识和训练回答问题。为用户查询提供有用、准确和详细的回复。", "defaultSystemPromptWithKB": "您是一个智能助手。请总结知识库的内容来回答问题。请列出知识库中的数据并详细回答。当所有知识库内容与问题无关时，您的回答必须包含这句话\"在知识库中没有找到您要找的答案！\"答案需要考虑聊天历史。\\n这里是知识库：\\n{knowledge}\\n以上是知识库。", "backToDialogs": "返回对话", "deleteSelected": "删除选中", "dialogsRange": "{{start}}-{{end}} 共 {{total}} 个对话", "llmModelTooltip": "为此对话选择语言模型", "llmModelRequired": "请选择LLM模型", "selectLLMModel": "选择LLM模型", "modelParameters": "模型参数", "temperature": "温度", "temperatureTooltip": "控制响应的随机性 (0-1)", "topP": "Top P", "topPTooltip": "通过核采样控制多样性", "presencePenalty": "存在惩罚", "presencePenaltyTooltip": "重复主题的惩罚", "frequencyPenalty": "频率惩罚", "frequencyPenaltyTooltip": "重复词汇的惩罚", "maxTokens": "最大令牌数", "maxTokensTooltip": "响应中的最大令牌数"}, "chat": {"title": "聊天", "sendMessage": "发送消息 (回车)", "stopGenerating": "停止生成", "uploadFile": "上传文件", "voiceInput": "语音输入", "enterMessage": "请输入您的消息...", "noMessages": "暂无消息", "copySuccess": "复制成功", "copyFailed": "复制失败，请手动复制", "welcome": "欢迎使用{{name}}", "defaultWelcome": "您好！我是您的助手，有什么可以帮助您的吗？", "noDialogSelected": "未选择对话", "selectDialogDesc": "请从对话页面选择一个对话开始聊天。", "goToDialogs": "前往对话", "dialogNotFound": "对话未找到", "backToConversations": "返回对话记录", "chatWithDialog": "与{{name}}聊天...", "configureLLMFirst": "请先配置LLM模型", "welcomeTo": "欢迎使用{{name}}", "model": "模型", "notConfigured": "未配置", "knowledgeBases": "知识库", "connected": "已连接{{count}}个"}, "conversations": {"title": "对话记录", "searchConversations": "搜索对话...", "noConversations": "未找到对话记录", "viewConversation": "查看", "continueChat": "继续聊天", "deleteConversation": "删除", "deleteConfirm": "确定要删除这个对话吗？", "deleteSuccess": "对话删除成功", "userMessages": "{{count}} 条用户消息", "aiMessages": "{{count}} 条AI消息", "lastMessage": "最后消息", "by": "来自", "manageDescription": "管理您的聊天对话和历史记录", "newConversation": "新对话", "totalConversations": "总对话数", "withMessages": "有消息的", "totalMessages": "总消息数", "recentlyActive": "最近活跃", "loading": "加载对话中...", "noConversationsFound": "未找到包含\"{{keywords}}\"的对话", "startNewConversation": "开始新对话", "chooseDialogAndStart": "选择对话并开始聊天", "selectDialogToStart": "选择一个对话开始对话", "searchDialogsPlaceholder": "按名称或描述搜索对话...", "noDialogsFoundFor": "未找到包含\"{{keywords}}\"的对话", "noDialogsAvailable": "没有可用的对话", "createFirstDialog": "创建您的第一个对话", "updated": "更新于", "manageDialogs": "管理对话"}, "aiModels": {"title": "AI模型", "availableModels": "可用模型", "myModels": "我的模型", "addModel": "添加模型", "configureDialog": "配置对话", "modelName": "模型名称", "provider": "提供商", "status": "状态", "actions": "操作", "active": "激活", "inactive": "未激活", "configure": "配置", "remove": "移除", "apiKey": "API密钥", "baseUrl": "基础URL", "enterApiKey": "请输入API密钥！", "enterBaseUrl": "输入基础URL", "apiKeyTooltip": "用于身份验证的API密钥", "baseUrlTooltip": "模型API端点的基础URL", "configureSuccess": "模型配置成功", "removeConfirm": "确定要移除这个模型吗？", "removeSuccess": "模型移除成功", "modelSettings": "模型设置", "configureFailed": "API密钥配置失败", "addFailed": "添加模型失败", "manageDescription": "管理您的AI模型提供商和配置", "configureFactory": "配置{{factory}}", "apiKeyPlaceholder": "输入API密钥（本地模型可选）", "availableProviders": "可用提供商", "setDefaultModels": "设置默认模型", "setDefaultSuccess": "默认模型设置成功！", "setDefaultFailed": "设置默认模型失败", "loadingProviders": "加载提供商中...", "loadProvidersFailed": "加载提供商失败：{{error}}", "noProviders": "没有可用的提供商", "loadingModels": "加载模型中...", "noModels": "尚未配置任何模型", "baseUrlRequired": "请输入基础URL！", "modelUID": "模型UID", "modelNameRequired": "请输入模型名称！", "enterModelName": "输入模型名称", "apiBaseUrl": "API基础URL", "apiBaseUrlRequired": "请输入API基础URL！", "enterApiBaseUrl": "输入API基础URL"}, "settings": {"title": "设置", "profile": "个人资料", "preferences": "偏好设置", "security": "安全", "about": "关于", "userInfo": "用户信息", "changePassword": "修改密码", "currentPassword": "当前密码", "newPassword": "新密码", "confirmPassword": "确认密码", "updateProfile": "更新资料", "updateSuccess": "设置更新成功", "manageDescription": "管理您的账户设置和系统信息", "profileSettings": "个人资料设置", "passwordSettings": "密码设置", "updateFailed": "更新个人资料失败", "passwordUpdateFailed": "更新密码失败", "changeAvatar": "更换头像", "nicknamePlaceholder": "请输入昵称", "emailRequired": "请输入邮箱！", "emailInvalid": "请输入有效的邮箱！", "emailPlaceholder": "请输入邮箱", "currentPasswordRequired": "请输入当前密码！", "currentPasswordPlaceholder": "请输入当前密码", "newPasswordRequired": "请输入新密码！", "passwordMinLength": "密码至少6个字符！", "newPasswordPlaceholder": "请输入新密码", "confirmPasswordRequired": "请确认新密码！", "passwordMismatch": "两次密码不匹配！", "confirmPasswordPlaceholder": "确认新密码", "updatePassword": "更新密码", "systemInformation": "系统信息", "versionInformation": "版本信息", "version": "版本", "userId": "用户ID", "created": "创建时间", "lastUpdated": "最后更新", "systemStatus": "系统状态"}, "messages": {"success": {"created": "创建成功", "updated": "更新成功", "deleted": "删除成功", "saved": "保存成功", "uploaded": "上传成功", "copied": "复制成功", "loginSuccess": "登录成功！"}, "error": {"createFailed": "创建失败", "updateFailed": "更新失败", "deleteFailed": "删除失败", "saveFailed": "保存失败", "uploadFailed": "上传失败", "copyFailed": "复制失败", "networkError": "网络错误", "unknownError": "未知错误"}, "validation": {"required": "此字段为必填项", "emailInvalid": "请输入有效的邮箱地址", "passwordTooShort": "密码至少需要6个字符", "passwordMismatch": "密码不匹配", "maxLength": "最多允许{{max}}个字符"}, "warning": {"enterQuestion": "请输入要测试的问题"}}, "placeholders": {"typeMessage": "在此输入您的消息...", "enterMessage": "请输入您的消息...", "searchPlaceholder": "搜索...", "selectOption": "请选择选项", "enterText": "请输入文本...", "optional": "（可选）"}, "tooltips": {"uploadFile": "上传文件", "voiceInput": "语音输入", "sendMessage": "发送消息（回车）", "stopGenerating": "停止生成", "copyToClipboard": "复制到剪贴板", "deleteItem": "删除项目", "editItem": "编辑项目", "viewItem": "查看项目"}, "status": {"active": "激活", "inactive": "未激活", "online": "在线", "offline": "离线", "pending": "待处理", "completed": "已完成", "failed": "失败", "processing": "处理中"}, "languages": {"english": "英语", "chinese": "中文", "japanese": "日语", "korean": "韩语"}, "systemPrompts": {"default": "您是一个智能助手。请总结知识库的内容来回答问题。请列出知识库中的数据并详细回答。当所有知识库内容都与问题无关时，您的回答必须包含这句话\"在知识库中没有找到您要找的答案！\"回答需要考虑聊天历史。\\n      以下是知识库：\\n      {knowledge}\\n      以上是知识库。", "noKnowledge": "您是一个有用的AI助手。请为用户问题提供准确和有用的回答。", "notFoundMessage": "在知识库中没有找到您要找的答案！"}, "textToImage": {"title": "AI文生图", "description": "基于AI的文本生成图像服务，输入文字描述即可生成高质量图像", "loading": "正在加载文生图服务...", "loadError": "无法加载文生图服务，请检查服务是否正常运行", "serviceUnavailable": "服务加载失败"}, "aiRead": {"title": "AI阅读", "uploadDocument": "上传文档", "documentContent": "文档内容", "preview": "预览", "summary": "总结", "pending": "待定", "chat": "聊天", "uploadArea": "点击或拖拽文件到此区域上传", "uploadHint": "支持PDF、DOC、DOCX、TXT等文档格式", "documentSummary": "文档总结", "askQuestion": "询问此文档", "generateSummary": "生成总结", "summaryPrompt": "请总结这个文档的主要内容，包括关键点、结论和重要信息。", "noDocumentUploaded": "尚未上传文档", "uploadFirst": "请先上传文档", "summaryGenerating": "正在生成总结...", "summaryGenerated": "总结生成成功", "askAboutDocument": "询问关于上传文档的问题", "typeQuestion": "输入关于文档的问题...", "documentQA": "文档问答", "uploadedFiles": "已上传文件", "maxFiles": "最多25个文件，每个50MB", "noDocumentSelected": "未选择文档", "clickToView": "点击上方文件查看内容", "uploadToStart": "上传文件开始使用", "uploadMore": "上传更多", "fileUploaded": "个文件上传成功", "maxFilesError": "最多允许上传25个文件，请先删除一些文件。", "unsupportedFormat": "不支持的文件格式: {{filename}}。请上传 PDF、DOC、DOCX、PPT、PPTX、TXT 或 MD 文件。", "fileTooLarge": "文件 {{filename}} 太大，最大支持 {{maxSize}}MB。", "uploadFailed": "文档上传失败", "uploadFailedError": "文档上传失败:", "fileSize": "文件大小:", "fileType": "文件类型:", "processingStatus": "处理状态:", "processingProgress": "处理进度:", "aiGeneratedSummary": "AI生成的内容摘要", "selectFileToView": "请选择一个文件查看详情", "startProcessing": "开始处理", "refreshStatus": "刷新状态", "deleteFile": "删除文件", "confirmDelete": "确认删除", "confirmDeleteMessage": "确定要删除文件 \"{{filename}}\" 吗？此操作不可恢复。", "description": "上传文档并通过摘要和问答获得AI驱动的洞察", "status": {"uploaded": "已上传", "converting_to_pdf": "转换PDF中", "calling_mineru": "调用解析API", "processing": "处理中", "saving_results": "保存结果中", "generating_summary": "生成摘要中", "completed": "已完成", "failed": "失败"}}, "documents": {"title": "文档管理", "description": "管理您上传的文档，支持预览和下载功能", "totalDocuments": "文档总数", "completed": "已完成", "processing": "处理中", "failed": "失败", "searchPlaceholder": "搜索文档...", "refresh": "刷新", "uploadDocument": "上传文档", "deleteSelected": "删除选中", "documentName": "文档名称", "type": "类型", "size": "大小", "status": "状态", "uploadTime": "上传时间", "actions": "操作", "preview": "预览", "download": "下载", "delete": "删除", "uploadSuccess": "文档上传成功", "uploadFailed": "文档上传失败", "deleteSuccess": "文档删除成功", "deleteFailed": "文档删除失败", "downloadStarted": "开始下载", "batchDeleteConfirm": "删除选中的文档？", "batchDeleteSuccess": "个文档删除成功", "batchDeleteFailed": "文档删除失败", "uploadHint": "支持PDF、DOC、DOCX、TXT、MD文件（最大10MB）", "showTotal": "个文档", "unknown": "未知", "mockContent": "{{name}}的模拟内容。在实际实现中，这将是真实的文档内容。", "parser": "解析器", "chunks": "文档块", "tokens": "令牌", "created": "创建时间", "thumbnail": "缩略图", "fullView": "全屏查看", "extractedContent": "提取内容", "extractedContentDesc": "以下内容已从文档中提取，用于AI处理。"}, "dialogAdvanced": {"modelSettings": "模型设置", "model": "模型", "modelTooltip": "为此对话选择语言模型", "selectModelRequired": "请选择LLM模型", "selectModelPlaceholder": "选择LLM模型", "modelParameters": "模型参数", "temperature": "温度", "temperatureTooltip": "控制回复的随机性", "topP": "Top P", "topPTooltip": "通过核采样控制多样性", "presencePenalty": "存在惩罚", "presencePenaltyTooltip": "重复主题的惩罚", "frequencyPenalty": "频率惩罚", "frequencyPenaltyTooltip": "重复词汇的惩罚", "maxTokens": "最大令牌数", "maxTokensTooltip": "回复中的最大令牌数", "parameterPreset": "参数预设", "parameterPresetTooltip": "选择参数预设配置", "precise": "精确", "evenly": "均衡", "creative": "创意", "custom": "自定义"}, "chatbot": {"title": "AI助手", "subtitle": "智能对话助手", "description": "基于汉邦高科的智能聊天机器人", "initializing": "正在初始化...", "noMessages": "暂无对话消息", "thinking": "正在思考中...", "inputPlaceholder": "请输入您的问题...", "send": "发送", "stop": "停止", "clear": "清空对话", "reset": "新增会话", "settings": "设置", "streamMode": "流式模式", "normalMode": "普通模式", "systemPrompt": "系统提示", "sessionInfo": "会话信息", "messageCount": "消息数量", "sessionId": "会话ID", "createdAt": "创建时间", "lastActive": "最后活跃", "defaultSystemPrompt": "你是汉邦高科的AI助手，专门帮助用户使用汉邦高科系统。请用中文回答问题，保持友好和专业的语调。", "error": {"sessionNotFound": "会话不存在", "messageEmpty": "消息不能为空", "sendFailed": "发送消息失败", "loadFailed": "加载失败", "networkError": "网络错误", "serverError": "服务器错误", "unauthorized": "未授权访问"}, "success": {"sessionCreated": "会话创建成功", "sessionCleared": "会话已清空", "sessionReset": "新会话已创建", "messageSent": "消息发送成功"}, "tips": {"enterToSend": "按Enter发送，Shift+Enter换行", "streamingMode": "流式模式可以实时看到回复", "clearWarning": "清空后将无法恢复对话记录", "resetWarning": "将创建一个新的会话"}, "features": "功能介绍"}}