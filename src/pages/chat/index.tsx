import React, { useEffect, useState } from 'react';
import {
  Card,
  Typography,
  Spin,
  Alert,
  Button,
  Space,
  Layout,
  message,
  Divider
} from 'antd';
import {
  ArrowLeftOutlined,
  MessageOutlined,
  SettingOutlined,
  RobotOutlined
} from '@ant-design/icons';
import { useNavigate, useSearchParams } from 'umi';
import AppLayout from '@/components/AppLayout';
import MessageList from '@/components/MessageList';
import MessageInput from '@/components/MessageInput';
import { useTranslate } from '@/hooks/use-i18n';
import { useFetchDialogList } from '@/hooks/use-dialog-hooks';
import {
  useChatState,
  useCreateConversation,
  useThumbup,
  useDeleteMessage,
  useConversation,
  useGetChatSearchParams,
  useFetchNextConversation,
  useSelectNextMessages
} from '@/hooks/use-chat-hooks';
import { IDialog } from '@/interfaces/dialog';
import { IMessage } from '@/interfaces/message';
import { buildMessageUuid } from '@/utils/chat';

const { Title, Text } = Typography;
const { Content, Sider } = Layout;

const ChatPage: React.FC = () => {
  const t = useTranslate();
  const navigate = useNavigate();

  // 使用新的URL参数获取hook（参考原始web）
  const { dialogId, conversationId, isNew } = useGetChatSearchParams();

  const [currentDialog, setCurrentDialog] = useState<IDialog | null>(null);

  // 获取Dialog列表来找到当前Dialog
  const { data: dialogs = [], isLoading } = useFetchDialogList({});

  // 聊天状态管理
  const chatState = useChatState(dialogId || '');
  const { updateConversation } = chatState;

  // 使用新的conversation获取hook（参考原始web）
  const { data: conversation } = useFetchNextConversation();

  // 使用派生消息管理（参考原始web）
  const {
    derivedMessages,
    loading: messagesLoading,
    addNewestAnswer,
    addNewestQuestion,
    removeLatestMessage,
    removeMessageById,
  } = useSelectNextMessages();

  // 创建对话
  const createConversation = useCreateConversation();

  // 点赞功能
  const thumbup = useThumbup();

  // 删除消息功能
  const deleteMessage = useDeleteMessage();

  useEffect(() => {
    if (dialogId && dialogs.length > 0) {
      // 使用id字段而不是dialog_id字段进行查找
      const dialog = dialogs.find((d: IDialog) => d.id === dialogId);
      setCurrentDialog(dialog || null);
    }
  }, [dialogId, conversationId, dialogs]);



  // 移除自动创建对话的逻辑，改为在发送第一条消息时创建

  const handleBackToConversations = () => {
    navigate('/conversations');
  };

  const handleSendMessage = async (content: string, files?: any[]) => {
    if (!currentDialog) return;

    // 创建用户消息
    const userMessage: IMessage = {
      id: buildMessageUuid({ role: 'user' }),
      content: content.trim(),
      role: 'user',
      doc_ids: files || [],
      create_time: new Date().toISOString(),
    };

    // 使用addNewestQuestion添加用户消息和空的助手消息
    addNewestQuestion(userMessage, '');

    // 然后调用chatState的发送逻辑，但不让它添加消息到界面
    // 在Continue Chat时，传递当前的derivedMessages作为历史消息
    // 创建一个包装函数来处理引用数据
    const handleStreamUpdate = (content: string, reference?: any) => {
      addNewestAnswer(content, reference);
    };

    try {
      // 确保使用正确的conversation id：
      // 1. 如果URL中有conversation_id且不是新对话，使用URL中的
      // 2. 如果是新对话或URL中没有conversation_id，使用state中的（可能为空，会创建新的）
      // 3. 一旦创建了新对话，立即更新URL以确保后续消息使用同一个conversation_id
      const effectiveConversationId = (isNew !== 'true' && conversationId) ? conversationId : chatState.currentConversation?.id;

      await chatState.sendMessageWithoutUI(content, files, handleStreamUpdate, derivedMessages, effectiveConversationId);

      // 如果是新对话，发送成功后立即更新URL包含conversation_id，确保后续消息使用同一个conversation
      if (isNew === 'true' && chatState.currentConversation?.id) {
        const newUrl = `/chat?dialog_id=${dialogId}&conversation_id=${chatState.currentConversation.id}`;
        navigate(newUrl, { replace: true });
      }
      // 如果原来URL中没有conversation_id，但现在有了，也要更新URL
      else if (!conversationId && chatState.currentConversation?.id) {
        const newUrl = `/chat?dialog_id=${dialogId}&conversation_id=${chatState.currentConversation.id}`;
        navigate(newUrl, { replace: true });
      }
    } catch (error) {
      console.error('Send message failed:', error);
      // 如果发送失败，移除最后添加的消息
      removeLatestMessage();
    }
  };

  const handleStopSending = () => {
    chatState.stopSending();
  };

  const handleThumbup = (messageId: string, thumbupValue: boolean) => {
    thumbup.mutate({ messageId, thumbup: thumbupValue });
  };

  const handleCopy = (content: string) => {
    message.success('Message copied to clipboard');
  };

  const handleTTS = (content: string) => {
    // TTS功能实现
    message.info('TTS feature coming soon');
  };

  // 按照原版ragflow的删除机制：删除消息后立即保存到后端
  const handleDeleteMessage = async (messageId: string) => {
    // 找到要删除的消息
    const messageToDelete = derivedMessages.find(msg => msg.id === messageId);
    if (!messageToDelete) {
      message.error('删除失败：消息不存在');
      return;
    }

    // 检查是否是欢迎消息，欢迎消息不允许删除
    const isWelcomeMessage = messageToDelete.content?.includes("Hi! I'm your assistant") ||
                            messageToDelete.content?.includes("您好！我是您的助手") ||
                            messageToDelete.content?.includes("how can I help you") ||
                            messageToDelete.content?.includes("有什么可以帮助您的吗") ||
                            messageToDelete.content?.includes("How can I help you") ||
                            messageToDelete.content?.includes("我是您的助手") ||
                            messageToDelete.content?.includes("我可以帮助您");

    if (isWelcomeMessage) {
      message.warning('欢迎消息不能删除');
      return;
    }

    // 如果是新对话，只做前端删除
    if (isNew === 'true') {
      removeMessageById(messageId);
      return;
    }

    // 对于已保存的对话，需要删除后立即保存到后端
    if (!conversationId) {
      message.error('删除失败：缺少对话ID');
      return;
    }

    try {
      // 按照原版ragflow的逻辑：只删除指定的消息和其直接配对的消息
      const messageIndex = derivedMessages.findIndex(msg => msg.id === messageId);
      if (messageIndex === -1) return;

      const messagesToDelete = [messageId];

      // 根据原版ragflow的逻辑，删除消息对
      if (messageToDelete.role === 'user') {
        // 如果删除用户消息，检查下一条是否是对应的助手回复
        if (messageIndex + 1 < derivedMessages.length) {
          const nextMessage = derivedMessages[messageIndex + 1];
          if (nextMessage.role === 'assistant') {
            messagesToDelete.push(nextMessage.id);
          }
        }
      } else if (messageToDelete.role === 'assistant') {
        // 如果删除助手消息，检查上一条是否是对应的用户消息
        if (messageIndex > 0) {
          const prevMessage = derivedMessages[messageIndex - 1];
          if (prevMessage.role === 'user') {
            messagesToDelete.push(prevMessage.id);
          }
        }
      }

      // 计算删除后的消息列表
      const updatedMessages = derivedMessages.filter(msg => !messagesToDelete.includes(msg.id));

      // 先更新前端状态（乐观更新）
      messagesToDelete.forEach(id => removeMessageById(id));

      // 立即保存到后端
      await updateConversation.mutateAsync({
        conversation_id: conversationId,
        message: updatedMessages,
        is_new: false,
      });

      message.success('消息删除成功');
    } catch (error) {
      console.error('Delete message failed:', error);
      message.error('删除失败，正在刷新页面...');
      // 如果保存失败，刷新页面恢复状态
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    }
  };

  const handleResend = (content: string) => {
    // 重新发送消息
    handleSendMessage(content, []);
  };

  if (isLoading) {
    return (
      <AppLayout>
        <div style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center', 
          height: '50vh' 
        }}>
          <Spin size="large" />
        </div>
      </AppLayout>
    );
  }

  if (!dialogId) {
    return (
      <AppLayout>
        <Card>
          <Alert
            message={t('chat.noDialogSelected', 'No Dialog Selected')}
            description={t('chat.selectDialogDesc', 'Please select a dialog from the dialogs page to start chatting.')}
            type="warning"
            showIcon
            action={
              <Button type="primary" onClick={handleBackToConversations}>
                {t('chat.goToDialogs', 'Go to Dialogs')}
              </Button>
            }
          />
        </Card>
      </AppLayout>
    );
  }

  if (!currentDialog) {
    return (
      <AppLayout>
        <Card>
          <Alert
            message={t('chat.dialogNotFound', 'Dialog Not Found')}
            description={
              <div>
                <p>Dialog with ID "{dialogId}" was not found.</p>
                <p>Available dialogs: {dialogs.length}</p>

              </div>
            }
            type="error"
            showIcon
            action={
              <Button type="primary" onClick={handleBackToConversations}>
                {t('chat.backToConversations', 'Go to Conversations')}
              </Button>
            }
          />
        </Card>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div style={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
        {/* Header */}
        <div style={{
          padding: '16px 24px',
          borderBottom: '1px solid #f0f0f0',
          background: '#fff'
        }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Space>
              <Button
                icon={<ArrowLeftOutlined />}
                onClick={handleBackToConversations}
                type="text"
              >
                {t('chat.backToConversations', 'Back to Conversations')}
              </Button>
              <Divider type="vertical" />
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <RobotOutlined style={{ marginRight: '8px', color: '#1890ff' }} />
                <div>
                  <Title level={4} style={{ margin: 0 }}>
                    {currentDialog.name}
                  </Title>
                  {currentDialog.description && (
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      {currentDialog.description}
                    </Text>
                  )}
                </div>
              </div>
            </Space>

            <Space>
              <Button
                icon={<SettingOutlined />}
                onClick={() => navigate(`/dialogs/${currentDialog.id}/view`)}
                type="text"
              >
                {t('navigation.settings')}
              </Button>
            </Space>
          </div>
        </div>

        {/* Chat Content */}
        <div style={{ flex: 1, display: 'flex', flexDirection: 'column', overflow: 'hidden' }}>
          {/* Error Display */}
          {chatState.error && (
            <Alert
              message={t('common.error', 'Error')}
              description={chatState.error}
              type="error"
              closable
              onClose={() => {
                // 清除错误状态
                chatState.setCurrentConversation(chatState.currentConversation);
              }}
              style={{ margin: '8px 16px' }}
            />
          )}

          {/* Messages Area */}
          <div style={{ flex: 1, overflow: 'hidden' }}>
            <MessageList
              messages={derivedMessages}
              loading={chatState.sending || messagesLoading}
              onThumbup={handleThumbup}
              onDelete={handleDeleteMessage}
              onCopy={handleCopy}
              onTTS={handleTTS}
              onResend={handleResend}
              showEmptyState={false}
              useKnowledgeMarkdown={true}
              isStreaming={chatState.sending}
            />
          </div>

          {/* Input Area */}
          <MessageInput
            onSend={handleSendMessage}
            onStop={handleStopSending}
            loading={chatState.sending}
            disabled={!currentDialog?.llm_id}
            placeholder={
              currentDialog?.llm_id
                ? t('chat.chatWithDialog', 'Chat with {{name}}...', { name: currentDialog.name })
                : t('chat.configureLLMFirst', 'Please configure LLM model first')
            }
          />
        </div>

        {/* Welcome Message */}
        {(!derivedMessages || derivedMessages.length === 0) && (
          <div style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            textAlign: 'center',
            zIndex: 1
          }}>
            <RobotOutlined style={{ fontSize: '64px', color: '#1890ff', marginBottom: '16px' }} />
            <Title level={3}>{t('chat.welcomeTo', 'Welcome to {{name}}', { name: currentDialog.name })}</Title>
            <Text type="secondary">
              {currentDialog.prompt_config?.prologue || t('chat.defaultWelcome')}
            </Text>
            <div style={{ marginTop: '16px' }}>
              <Space direction="vertical" size="small">
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  <strong>{t('chat.model', 'Model')}:</strong> {currentDialog.llm_id || t('chat.notConfigured', 'Not configured')}
                </Text>
                {currentDialog.kb_ids && currentDialog.kb_ids.length > 0 && (
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    <strong>{t('chat.knowledgeBases', 'Knowledge Bases')}:</strong> {t('chat.connected', '{{count}} connected', { count: currentDialog.kb_ids.length })}
                  </Text>
                )}
              </Space>
            </div>
          </div>
        )}
      </div>
    </AppLayout>
  );
};

export default ChatPage;
