.aiReadContent {
  background: #f0f2f5;
  min-height: calc(100vh - 64px);
  padding: 24px;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
}

.header {
  margin-bottom: 3px; // 减少到50% (6px -> 3px)
  text-align: center;

  h2 {
    margin-bottom: 2px; // 减少到50% (4px -> 2px)
    color: #1890ff;
    font-size: 18px; // 进一步减小字体
  }
}

.fileUploadSection {
  margin-bottom: 24px;

  .uploadButton {
    margin-bottom: 16px;

    .compactUploadArea {
      height: 30px; // 进一步减少到50% (60px -> 30px)
      border: 2px dashed #d9d9d9;
      border-radius: 8px;
      transition: border-color 0.3s ease;

      &:hover {
        border-color: #1890ff;
      }

      // 重新布局为水平排列
      .ant-upload-drag-container {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
        padding: 0 16px;
      }

      .ant-upload-drag-icon {
        font-size: 16px; // 减小图标
        color: #1890ff;
        margin-bottom: 0;
        margin-right: 8px; // 减小间距
      }

      .upload-content {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
      }

      .ant-upload-text {
        font-size: 12px; // 减小字体
        font-weight: 500;
        color: #262626;
        margin-bottom: 1px;
        line-height: 1.1;
      }

      .ant-upload-hint {
        color: #8c8c8c;
        font-size: 10px; // 减小字体
        line-height: 1.1;
      }
    }
  }

  .fileList {
    h4 {
      margin-bottom: 12px;
      color: #262626;
    }

    .fileItems {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      max-height: 200px;
      overflow-y: auto;

      .fileItem {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px 12px;
        background: #fafafa;
        border: 1px solid #f0f0f0;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.3s ease;
        min-width: 200px;
        max-width: 300px;

        &:hover {
          background: #e6f7ff;
          border-color: #1890ff;
        }

        &.active {
          background: #e6f7ff;
          border-color: #1890ff;
          box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
        }

        .fileInfo {
          flex: 1;
          min-width: 0;

          .fileName {
            font-size: 13px;
            font-weight: 500;
            color: #262626;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .fileSize {
            font-size: 11px;
            color: #8c8c8c;
          }
        }

        .fileActions {
          margin-left: 8px;

          .ant-btn {
            padding: 0;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            line-height: 1;
          }
        }
      }
    }
  }
}

.mainContent {
  min-height: 600px;
}

.leftPanel {
  .documentCard {
    height: 100%;
    min-height: 600px;
    
    .ant-card-body {
      height: calc(100% - 57px);
      display: flex;
      flex-direction: column;
    }
  }
  
  .uploadSection {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .uploadArea {
      width: 100%;
      height: 300px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      border: 2px dashed #d9d9d9;
      border-radius: 8px;
      transition: border-color 0.3s ease;
      
      &:hover {
        border-color: #1890ff;
      }
      
      .ant-upload-drag-icon {
        font-size: 48px;
        color: #1890ff;
        margin-bottom: 16px;
      }
      
      .ant-upload-text {
        font-size: 16px;
        font-weight: 500;
        color: #262626;
        margin-bottom: 8px;
      }
      
      .ant-upload-hint {
        color: #8c8c8c;
        font-size: 14px;
      }
    }
  }
  
  .noDocumentSelected {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
  }

  .documentContent {
    flex: 1;
    display: flex;
    flex-direction: column;

    .documentHeader {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 16px;

      .documentInfo {
        flex: 1;

        h4 {
          margin-bottom: 8px;
          color: #1890ff;
        }
      }
    }

    .documentText {
      flex: 1;
      display: flex;
      flex-direction: column;

      .contentHeader {
        margin-bottom: 12px;
        padding-bottom: 8px;
        border-bottom: 1px solid #f0f0f0;
      }

      .contentBody {
        flex: 1;
        padding: 16px;
        background: #fafafa;
        border-radius: 8px;
        border: 1px solid #f0f0f0;
        max-height: 400px;
        overflow-y: auto;

        span {
          line-height: 1.6;
          white-space: pre-wrap;
          word-break: break-word;
          font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        // 自定义滚动条样式
        &::-webkit-scrollbar {
          width: 6px;
        }

        &::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 3px;
        }

        &::-webkit-scrollbar-thumb {
          background: #c1c1c1;
          border-radius: 3px;

          &:hover {
            background: #a8a8a8;
          }
        }
      }
    }
  }

  // 左侧面板摘要样式
  .summaryHeader {
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #f0f0f0;
  }

  .leftPanelSummary {
    background: #f5f5f5;
    padding: 16px;
    border-radius: 8px;
    border: 1px solid #e8e8e8;
    margin-top: 8px;
    // 移除高度限制，让内容完整显示
    // max-height: 300px;
    // overflow-y: auto;

    // 移除滚动条样式，因为不再需要滚动
    // &::-webkit-scrollbar {
    //   width: 6px;
    // }
    //
    // &::-webkit-scrollbar-track {
    //   background: #f1f1f1;
    //   border-radius: 3px;
    // }
    //
    // &::-webkit-scrollbar-thumb {
    //   background: #c1c1c1;
    //   border-radius: 3px;
    //
    //   &:hover {
    //     background: #a8a8a8;
    //   }
    // }

    // 左侧面板markdown内容样式
    .markdown-content {
      font-size: 14px;
      line-height: 1.6;
      color: #333;

      // 调整标题样式
      h1, h2, h3, h4, h5, h6 {
        margin-top: 0.8em;
        margin-bottom: 0.4em;
        color: #262626;

        &:first-child {
          margin-top: 0;
        }
      }

      h1 { font-size: 1.2em; }
      h2 { font-size: 1.1em; }
      h3 { font-size: 1.05em; }
      h4, h5, h6 { font-size: 1em; }

      // 调整段落间距
      p {
        margin-bottom: 0.6em;

        &:last-child {
          margin-bottom: 0;
        }
      }

      // 调整列表样式
      ul, ol {
        margin-bottom: 0.6em;
        padding-left: 1.2em;
      }

      li {
        margin-bottom: 0.2em;
      }

      // 调整代码块样式
      pre {
        background-color: #f6f8fa;
        border: 1px solid #e1e4e8;
        border-radius: 6px;
        padding: 8px 12px;
        margin: 0.6em 0;
        font-size: 13px;
        overflow-x: auto;
      }

      // 调整行内代码样式
      code {
        background-color: #f6f8fa;
        border: 1px solid #e1e4e8;
        border-radius: 3px;
        padding: 1px 4px;
        font-size: 13px;
      }

      // 调整引用样式
      blockquote {
        border-left: 3px solid #1890ff;
        background-color: #f6f8fa;
        padding: 6px 10px;
        margin: 0.6em 0;
        border-radius: 0 4px 4px 0;
      }

      // 调整表格样式
      table {
        font-size: 13px;
        margin: 0.6em 0;

        th, td {
          padding: 4px 8px;
        }
      }

      // 调整链接样式
      a {
        color: #1890ff;

        &:hover {
          color: #40a9ff;
        }
      }
    }
  }
}

.rightPanel {
  .tabsCard {
    height: 100%;
    min-height: 600px;
    
    .ant-card-body {
      height: calc(100% - 57px);
      padding: 0;
    }
    
    .ant-tabs {
      height: 100%;
      
      .ant-tabs-nav {
        margin-bottom: 0;
        padding: 0 24px;
        background: #fafafa;
        border-bottom: 1px solid #f0f0f0;
        
        .ant-tabs-tab {
          padding: 16px 20px;
          font-weight: 500;
          
          &.ant-tabs-tab-active {
            background: #fff;
            border-bottom: 2px solid #1890ff;
          }
        }
      }
      
      .ant-tabs-content-holder {
        height: calc(100% - 57px);
        
        .ant-tabs-content {
          height: 100%;
          
          .ant-tabs-tabpane {
            height: 100%;
            padding: 24px;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 992px) {
  .mainContent {
    .leftPanel {
      margin-bottom: 24px;

      .documentCard {
        min-height: 400px;
      }
    }

    .rightPanel {
      .tabsCard {
        min-height: 500px;
      }
    }
  }

  .fileUploadSection {
    .fileList {
      .fileItems {
        .fileItem {
          min-width: 150px;
          max-width: 200px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .aiReadContent {
    padding: 16px;
  }

  .header {
    margin-bottom: 16px;

    h2 {
      font-size: 20px;
    }
  }

  .fileUploadSection {
    .uploadButton {
      .compactUploadArea {
        height: 50px; // 移动端进一步减小高度

        .ant-upload-drag-container {
          padding: 0 12px;
        }

        .ant-upload-drag-icon {
          font-size: 20px;
          margin-right: 8px;
        }

        .ant-upload-text {
          font-size: 12px;
        }

        .ant-upload-hint {
          font-size: 10px;
        }
      }
    }

    .fileList {
      .fileItems {
        .fileItem {
          min-width: 120px;
          max-width: 150px;

          .fileInfo {
            .fileName {
              font-size: 12px;
            }

            .fileSize {
              font-size: 10px;
            }
          }
        }
      }
    }
  }

  .leftPanel {
    .documentContent {
      .documentHeader {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
      }

      .documentText {
        .contentBody {
          max-height: 250px;
        }
      }
    }
  }
}
