import {
  useLogin,
  useLoginChannels,
  useLoginWithChannel,
  useRegister,
} from '@/hooks/login-hooks';
import AuthGuard from '@/components/AuthGuard';
import { rsaPsw } from '@/utils';
import { Button, Checkbox, Form, Input, Card, Typography, Space, Divider } from 'antd';
import { useEffect, useState } from 'react';
import { useNavigate } from 'umi';
import { useTranslate } from '@/hooks/use-i18n';
import LanguageSwitcher from '@/components/LanguageSwitcher';
import styles from './index.less';

const { Title, Text } = Typography;

const LoginContent = () => {
  const [title, setTitle] = useState('login');
  const [form] = Form.useForm();
  const t = useTranslate();
  const { login, loading: signLoading } = useLogin();
  const { register, loading: registerLoading } = useRegister();
  const { channels, loading: channelsLoading } = useLoginChannels();
  const { login: loginWithChannel, loading: loginWithChannelLoading } =
    useLoginWithChannel();
  const loading =
    signLoading ||
    registerLoading ||
    channelsLoading ||
    loginWithChannelLoading;

  const handleLoginWithChannel = async (channel: string) => {
    await loginWithChannel(channel);
  };

  const changeTitle = () => {
    setTitle((title) => (title === 'login' ? 'register' : 'login'));
  };

  const onCheck = async () => {
    try {
      const params = await form.validateFields();

      const rsaPassWord = rsaPsw(params.password) as string;

      if (title === 'login') {
        const code = await login({
          email: `${params.email}`.trim(),
          password: rsaPassWord,
        });
        // 登录成功后，useAuth hook会自动处理跳转
        // 不需要在这里手动跳转
      } else {
        const code = await register({
          nickname: params.nickname,
          email: params.email,
          password: rsaPassWord,
        });
        if (code === 0) {
          setTitle('login');
        }
      }
    } catch (errorInfo) {
      console.log('Failed:', errorInfo);
    }
  };

  return (
    <div className={styles.loginPage}>
      {/* 语言切换器 */}
      <div className={styles.languageSwitcher}>
        <LanguageSwitcher />
      </div>

      <div className={styles.loginContainer}>
        <Card className={styles.loginCard}>
          <div className={styles.loginHeader}>
            <Title level={2} className={styles.loginTitle}>
              {title === 'login' ? t('auth.signIn') : t('auth.signUp')}
            </Title>
            <Text type="secondary">
              {title === 'login'
                ? t('auth.welcomeBack')
                : t('auth.createAccountDesc')}
            </Text>
          </div>

          <Form
            form={form}
            layout="vertical"
            name="auth_form"
            className={styles.loginForm}
          >
            <Form.Item
              name="email"
              label={t('auth.email')}
              rules={[
                { required: true, message: t('auth.emailRequired') },
                { type: 'email', message: t('auth.emailInvalid') }
              ]}
            >
              <Input size="large" placeholder={t('auth.enterEmail')} />
            </Form.Item>

            {title === 'register' && (
              <Form.Item
                name="nickname"
                label={t('auth.nickname')}
                rules={[{ required: true, message: t('auth.nicknameRequired') }]}
              >
                <Input size="large" placeholder={t('auth.enterNickname')} />
              </Form.Item>
            )}

            <Form.Item
              name="password"
              label={t('auth.password')}
              rules={[{ required: true, message: t('auth.passwordRequired') }]}
            >
              <Input.Password
                size="large"
                placeholder={t('auth.enterPassword')}
                onPressEnter={onCheck}
              />
            </Form.Item>
            
            {title === 'login' && (
              <Form.Item name="remember" valuePropName="checked">
                <Checkbox>{t('auth.rememberMe')}</Checkbox>
              </Form.Item>
            )}

            <Form.Item>
              <Button
                type="primary"
                block
                size="large"
                onClick={onCheck}
                loading={loading}
                className={styles.submitButton}
              >
                {title === 'login' ? t('auth.signIn') : t('auth.createAccount')}
              </Button>
            </Form.Item>

            <div className={styles.switchMode}>
              {title === 'login' ? (
                <Text>
                  {t('auth.dontHaveAccount')}{' '}
                  <Button type="link" onClick={changeTitle} className={styles.linkButton}>
                    {t('auth.signUp')}
                  </Button>
                </Text>
              ) : (
                <Text>
                  {t('auth.alreadyHaveAccount')}{' '}
                  <Button type="link" onClick={changeTitle} className={styles.linkButton}>
                    {t('auth.signIn')}
                  </Button>
                </Text>
              )}
            </div>

            {title === 'login' && channels && channels.length > 0 && (
              <>
                <Divider>{t('auth.orContinueWith', 'Or continue with')}</Divider>
                <Space direction="vertical" style={{ width: '100%' }}>
                  {channels.map((item) => (
                    <Button
                      key={item.channel}
                      block
                      size="large"
                      onClick={() => handleLoginWithChannel(item.channel)}
                      className={styles.oauthButton}
                    >
                      {t('auth.signInWith', 'Sign in with {{provider}}', { provider: item.display_name })}
                    </Button>
                  ))}
                </Space>
              </>
            )}
          </Form>
        </Card>
      </div>
    </div>
  );
};

const Login = () => {
  return (
    <AuthGuard requireAuth={false}>
      <LoginContent />
    </AuthGuard>
  );
};

export default Login;
