.chatBotPage {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;

  .pageHeader {
    margin-bottom: 24px;
    padding: 24px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .headerIcon {
      font-size: 32px;
      color: #667eea;
    }
  }

  .content {
    .chatCard {
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      
      .ant-card-body {
        padding: 0;
      }
    }
  }

  .featureList {
    .featureItem {
      margin-bottom: 16px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .statsGrid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;

    .statItem {
      text-align: center;
      padding: 12px;
      background: #f8f9fa;
      border-radius: 8px;
    }
  }

  .tipsList {
    .tipItem {
      margin-bottom: 8px;
      line-height: 1.6;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .chatBotPage {
    padding: 16px;

    .pageHeader {
      padding: 16px;
      margin-bottom: 16px;

      .headerIcon {
        font-size: 24px;
      }
    }

    .content {
      .chatCard {
        .ant-card-body {
          padding: 8px;
        }
      }
    }

    .statsGrid {
      grid-template-columns: 1fr;
      gap: 12px;
    }
  }
}

// 暗色主题支持
@media (prefers-color-scheme: dark) {
  .chatBotPage {
    background: #1f1f1f;

    .pageHeader {
      background: #262626;
      color: #d9d9d9;
    }

    .content {
      .chatCard {
        background: #262626;
        border-color: #404040;
      }
    }

    .statsGrid {
      .statItem {
        background: #1f1f1f;
        color: #d9d9d9;
      }
    }
  }
}
