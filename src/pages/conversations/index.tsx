import React, { useState } from 'react';
import {
  Card,
  Button,
  Input,
  Space,
  Typography,
  Empty,
  Spin,
  Row,
  Col,
  Statistic,
  Collapse,
  Badge,
} from 'antd';
import {
  PlusOutlined,
  MessageOutlined,
  UserOutlined,
  RobotOutlined,
  ClockCircleOutlined,
} from '@ant-design/icons';
import { useNavigate } from 'umi';
import AppLayout from '@/components/AppLayout';
import ConversationCard from './components/ConversationCard';
import DialogSelector from './components/DialogSelector';
import { useConversationsList, useDeleteConversation } from '@/hooks/use-conversations-hooks';
import { useFetchDialogList } from '@/hooks/use-dialog-hooks';
import { IDialog } from '@/interfaces/dialog';
import { useTranslate } from '@/hooks/use-i18n';
import styles from './index.less';

const { Title, Text } = Typography;
const { Search } = Input;
const { Panel } = Collapse;

const ConversationsContent: React.FC = () => {
  const t = useTranslate();
  const navigate = useNavigate();
  const [searchKeywords, setSearchKeywords] = useState('');
  const [dialogSelectorVisible, setDialogSelectorVisible] = useState(false);

  // 获取conversations列表
  const { data: conversations = [], isLoading: loading } = useConversationsList({
    keywords: searchKeywords,
    page_size: 100,
  });

  // 删除conversation
  const deleteConversation = useDeleteConversation();

  // 获取dialogs列表用于快速创建对话
  const { data: dialogs = [] } = useFetchDialogList({});

  const handleCreateConversation = () => {
    if (dialogs.length === 0) {
      // 如果没有dialogs，跳转到dialogs页面创建新的dialog
      navigate('/dialogs');
    } else if (dialogs.length === 1) {
      // 如果只有一个dialog，直接使用它
      navigate(`/chat?dialog_id=${dialogs[0].id}`);
    } else {
      // 如果有多个dialogs，显示选择器
      setDialogSelectorVisible(true);
    }
  };

  const handleDialogSelect = (dialog: IDialog) => {
    setDialogSelectorVisible(false);
    navigate(`/chat?dialog_id=${dialog.id}`);
  };

  const handleDialogSelectorCancel = () => {
    setDialogSelectorVisible(false);
  };

  const handleSearch = (value: string) => {
    setSearchKeywords(value);
  };

  const handleDeleteConversation = (conversationId: string) => {
    deleteConversation.mutate(conversationId);
  };

  // 按Dialog分组conversations
  const groupedConversations = React.useMemo(() => {
    const groups: Record<string, {
      dialog: any;
      conversations: (typeof conversations)[0][];
    }> = {};

    conversations.forEach(conversation => {
      const dialogId = conversation.dialog_id;
      if (!groups[dialogId]) {
        const dialog = dialogs.find(d => d.id === dialogId);
        groups[dialogId] = {
          dialog: dialog || { id: dialogId, name: 'Unknown Dialog' },
          conversations: [],
        };
      }
      groups[dialogId].conversations.push(conversation);
    });

    // 按dialog名称排序
    return Object.values(groups).sort((a, b) =>
      a.dialog.name.localeCompare(b.dialog.name)
    );
  }, [conversations, dialogs]);

  return (
    <div className={styles.conversationsPage}>
      {/* 页面头部 */}
      <div className={styles.pageHeader}>
        <div className={styles.headerContent}>
          <div className={styles.headerLeft}>
            <Title level={2} className={styles.title}>
              <MessageOutlined className={styles.icon} />
              {t('conversations.title')}
            </Title>
            <Text type="secondary" className={styles.description}>
              {t('conversations.manageDescription', 'Manage your chat conversations and history')}
            </Text>
          </div>
          <div className={styles.headerRight}>
            <Search
              placeholder={t('conversations.searchConversations')}
              allowClear
              className={styles.searchInput}
              value={searchKeywords}
              onChange={(e) => setSearchKeywords(e.target.value)}
              onSearch={handleSearch}
              loading={loading}
            />
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleCreateConversation}
              className={styles.createButton}
            >
              {dialogs.length === 0
                ? t('dialog.createDialog')
                : dialogs.length === 1
                  ? t('conversations.newConversation', 'New Conversation')
                  : t('conversations.newConversation', 'New Conversation')
              }
            </Button>
          </div>
        </div>
      </div>

      {/* 统计信息 */}
      {!loading && conversations.length > 0 && (
        <Row gutter={16} style={{ marginBottom: '24px' }}>
          <Col span={6}>
            <Card>
              <Statistic
                title={t('conversations.totalConversations', 'Total Conversations')}
                value={conversations.length}
                prefix={<MessageOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title={t('conversations.withMessages', 'With Messages')}
                value={conversations.filter(c => c.message && c.message.length > 0).length}
                prefix={<UserOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title={t('conversations.totalMessages', 'Total Messages')}
                value={conversations.reduce((sum, c) => sum + (c.message?.length || 0), 0)}
                prefix={<RobotOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title={t('conversations.recentlyActive', 'Recently Active')}
                value={conversations.filter(c => {
                  const updateTime = new Date(c.update_time);
                  const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
                  return updateTime > oneDayAgo;
                }).length}
                prefix={<ClockCircleOutlined />}
              />
            </Card>
          </Col>
        </Row>
      )}

      {/* 内容区域 */}
      <Card className={styles.contentCard}>
        {loading ? (
          <div className={styles.loadingContainer}>
            <Spin size="large" />
            <div className={styles.loadingText}>
              <Text type="secondary">{t('conversations.loading', 'Loading conversations...')}</Text>
            </div>
          </div>
        ) : conversations.length === 0 ? (
          <div className={styles.emptyContainer}>
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={
                searchKeywords ?
                  t('conversations.noConversationsFound', 'No conversations found for "{{keywords}}"', { keywords: searchKeywords }) :
                  t('conversations.noConversations')
              }
            >
              {!searchKeywords && (
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleCreateConversation}
                >
                  {dialogs.length === 0
                    ? t('dialog.createFirstDialog', 'Create Your First Dialog')
                    : dialogs.length === 1
                      ? t('conversations.startNewConversation', 'Start New Conversation')
                      : t('conversations.chooseDialogAndStart', 'Choose Dialog & Start Chat')
                  }
                </Button>
              )}
            </Empty>
          </div>
        ) : (
          <Collapse
            defaultActiveKey={groupedConversations.map((_, index) => index.toString())}
            className={styles.dialogGroups}
          >
            {groupedConversations.map((group, index) => (
              <Panel
                header={
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <span style={{ fontWeight: 'bold', fontSize: '16px' }}>
                      {group.dialog.name}
                    </span>
                    <Badge
                      count={group.conversations.length}
                      style={{ backgroundColor: '#1890ff' }}
                    />
                  </div>
                }
                key={index.toString()}
              >
                <Row gutter={[12, 12]} className={styles.conversationsGrid}>
                  {group.conversations.map((conversation) => (
                    <Col xs={24} sm={12} md={8} lg={6} xl={4} xxl={3} key={conversation.id} className={styles.conversationCol}>
                      <ConversationCard
                        conversation={conversation}
                        onDelete={handleDeleteConversation}
                      />
                    </Col>
                  ))}
                </Row>
              </Panel>
            ))}
          </Collapse>
        )}
      </Card>

      {/* Dialog选择器 */}
      <DialogSelector
        visible={dialogSelectorVisible}
        dialogs={dialogs}
        onSelect={handleDialogSelect}
        onCancel={handleDialogSelectorCancel}
        loading={loading}
      />
    </div>
  );
};

const ConversationsPage: React.FC = () => {
  return (
    <AppLayout
      showSearch={false}
    >
      <ConversationsContent />
    </AppLayout>
  );
};

export default ConversationsPage;
