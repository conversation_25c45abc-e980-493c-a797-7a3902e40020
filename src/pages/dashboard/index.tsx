import { useAuth } from '@/hooks/auth-hooks';
import { useLogout } from '@/hooks/login-hooks';
import AppLayout from '@/components/AppLayout';
import { useNavigate } from 'umi';
import { useTranslate } from '@/hooks/use-i18n';
import {
  BookOutlined,
  DatabaseOutlined,
  MessageOutlined,
  SettingOutlined,
  UserOutlined,
  LogoutOutlined,
  FileTextOutlined,
  RobotOutlined,
} from '@ant-design/icons';
import {
  Avatar,
  Dropdown,
  Typography,
  Card,
  Row,
  Col,
  Statistic,
  Button,
  Space,
  List,
  Tag,
  message,
} from 'antd';
import { useState } from 'react';
import styles from './index.less';

const { Title, Text } = Typography;

const DashboardContent = () => {
  const t = useTranslate();
  const { userInfo } = useAuth();
  const { logout } = useLogout();
  const navigate = useNavigate();

  const handleLogout = async () => {
    await logout();
  };

  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: t('settings.profile'),
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: t('navigation.settings'),
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: t('auth.signOut'),
      onClick: handleLogout,
    },
  ];

  const menuItems = [
    {
      key: 'dashboard',
      icon: <DatabaseOutlined />,
      label: t('navigation.dashboard'),
    },
    {
      key: 'conversations',
      icon: <MessageOutlined />,
      label: t('navigation.conversations'),
    },
    {
      key: 'knowledge',
      icon: <BookOutlined />,
      label: t('navigation.knowledgeBase'),
      onClick: () => navigate('/knowledge'),
    },
    // {
    //   key: 'documents',
    //   icon: <FileTextOutlined />,
    //   label: t('navigation.documents'),
    // },
    {
      key: 'models',
      icon: <RobotOutlined />,
      label: t('navigation.aiModels'),
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: t('navigation.settings'),
    },
  ];

  const quickActions = [
    {
      title: t('dashboard.createKnowledgeBase', 'Create Knowledge Base'),
      description: t('dashboard.createKnowledgeBaseDesc', 'Start building your knowledge repository'),
      icon: <BookOutlined />,
      color: '#1890ff',
      action: () => navigate('/knowledge'),
    },
    {
      title: t('dashboard.manageDocuments', 'Manage Documents'),
      description: t('dashboard.manageDocumentsDesc', 'Upload and organize your documents'),
      icon: <FileTextOutlined />,
      color: '#52c41a',
      action: () => navigate('/knowledge'),
    },
    {
      title: t('dashboard.startConversation', 'Start Conversation'),
      description: t('dashboard.startConversationDesc', 'Chat with your AI assistant'),
      icon: <MessageOutlined />,
      color: '#722ed1',
      action: () => {
        message.info(t('common.comingSoon'));
      },
    },
    {
      title: t('dashboard.configureModels', 'Configure Models'),
      description: t('dashboard.configureModelsDesc', 'Set up your AI models'),
      icon: <RobotOutlined />,
      color: '#fa8c16',
      action: () => {
        message.info(t('common.comingSoon'));
      },
    },
  ];

  const recentActivities = [
    {
      title: t('dashboard.knowledgeBaseCreated', 'Knowledge Base created'),
      time: '2 hours ago',
      type: 'create',
    },
    {
      title: t('dashboard.documentUploaded', 'Document uploaded'),
      time: '4 hours ago',
      type: 'upload',
    },
    {
      title: t('dashboard.conversationCompleted', 'Conversation with AI completed'),
      time: '1 day ago',
      type: 'chat',
    },
    {
      title: t('dashboard.modelConfigUpdated', 'Model configuration updated'),
      time: '2 days ago',
      type: 'config',
    },
  ];

  return (
    <div className={styles.dashboardContent}>
      <div className={styles.welcomeSection}>
        <Typography.Title level={2}>{t('dashboard.welcomeBack', 'Welcome back, {{name}}!', { name: userInfo?.name || t('common.user') })}</Typography.Title>
        <Typography.Text type="secondary">
          {t('dashboard.manageDescription', 'Manage your knowledge base and interact with AI-powered insights')}
        </Typography.Text>
      </div>

      <Row gutter={[24, 24]} className={styles.statsRow}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title={t('dashboard.knowledgeBases', 'Knowledge Bases')}
              value={5}
              prefix={<BookOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title={t('navigation.documents')}
              value={128}
              prefix={<FileTextOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title={t('navigation.conversations')}
              value={42}
              prefix={<MessageOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title={t('navigation.aiModels')}
              value={3}
              prefix={<RobotOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[24, 24]}>
        <Col xs={24} lg={16}>
          <Card title={t('dashboard.quickActions', 'Quick Actions')} className={styles.quickActionsCard}>
            <Row gutter={[16, 16]}>
              {quickActions.map((action, index) => (
                <Col xs={24} sm={12} key={index}>
                  <Card
                    hoverable
                    className={styles.quickActionCard}
                    onClick={action.action}
                  >
                    <div className={styles.actionContent}>
                      <div
                        className={styles.actionIcon}
                        style={{ color: action.color }}
                      >
                        {action.icon}
                      </div>
                      <div>
                        <Title level={5} style={{ margin: 0 }}>
                          {action.title}
                        </Title>
                        <Text type="secondary">{action.description}</Text>
                      </div>
                    </div>
                  </Card>
                </Col>
              ))}
            </Row>
          </Card>
        </Col>
        
        <Col xs={24} lg={8}>
          <Card title={t('dashboard.recentActivities', 'Recent Activities')} className={styles.activitiesCard}>
            <List
              dataSource={recentActivities}
              renderItem={(item) => (
                <List.Item>
                  <List.Item.Meta
                    title={item.title}
                    description={item.time}
                  />
                  <Tag color={
                    item.type === 'create' ? 'blue' :
                    item.type === 'upload' ? 'green' :
                    item.type === 'chat' ? 'purple' : 'orange'
                  }>
                    {t(`dashboard.activityTypes.${item.type}`, item.type)}
                  </Tag>
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

const Dashboard = () => {
  return (
    <AppLayout showSearch={false}>
      <DashboardContent />
    </AppLayout>
  );
};

export default Dashboard;
