.knowledgePage {
  min-height: 100vh;
  background: #f0f2f5;
}

.pageHeader {
  background: #fff;
  padding: 24px;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.headerContent {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  max-width: 1200px;
  margin: 0 auto;
}

.headerLeft {
  h2 {
    color: #1f2937;
    font-weight: 600;
    margin-bottom: 8px;
  }
}

.headerRight {
  display: flex;
  align-items: center;
  gap: 16px;
}

.pageContent {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
}

.statsSection {
  margin-bottom: 32px;
  
  .ant-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    
    .ant-statistic-title {
      color: #6b7280;
      font-weight: 500;
    }
    
    .ant-statistic-content {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }
}

.knowledgeGrid {
  .loading {
    text-align: center;
    padding: 20px;
    color: #666;
  }
  
  .endMessage {
    text-align: center;
    padding: 20px;
  }
}

.knowledgeCard {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  border: 1px solid #e8e8e8;
  cursor: pointer;

  &:hover {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
    border-color: #1890ff;
  }

  &:active {
    transform: translateY(-1px);
  }
  
  .ant-card-body {
    padding: 20px;
  }
  
  .ant-card-actions {
    border-top: 1px solid #f0f0f0;
    background: #fafafa;
    
    li {
      margin: 8px 0;
      
      .ant-btn {
        border: none;
        box-shadow: none;
        height: auto;
        padding: 4px 8px;
        
        &:hover {
          color: #1890ff;
          background: rgba(24, 144, 255, 0.1);
        }
      }
    }
  }
}

.cardContent {
  height: 200px;
  display: flex;
  flex-direction: column;
}

.cardHeader {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 16px;
}

.cardIcon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
  flex-shrink: 0;
}

.cardTitle {
  flex: 1;
  min-width: 0;
  
  h5 {
    margin: 0 0 4px 0;
    color: #1f2937;
    font-weight: 600;
  }
}

.cardTime {
  font-size: 12px;
  color: #9ca3af;
}

.cardDescription {
  flex: 1;
  margin-bottom: 16px;
  
  .ant-typography {
    line-height: 1.5;
    color: #6b7280;
  }
}

.cardStats {
  margin-bottom: 12px;
  
  span {
    color: #6b7280;
    font-size: 12px;
    display: inline-flex;
    align-items: center;
    gap: 4px;
  }
}

.statsDivider {
  color: #d1d5db;
  margin: 0 8px;
}

.cardTags {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
  
  .ant-tag {
    margin: 0;
    border-radius: 4px;
    font-size: 11px;
    padding: 2px 6px;
    line-height: 1.2;
  }
}

@media (max-width: 768px) {
  .pageHeader {
    padding: 16px;
  }
  
  .headerContent {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .headerRight {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
    
    .ant-input-search {
      width: 100%;
    }
  }
  
  .pageContent {
    padding: 16px;
  }
  
  .statsSection {
    margin-bottom: 24px;
    
    .ant-col {
      margin-bottom: 12px;
    }
  }
  
  .knowledgeCard {
    margin-bottom: 16px;
    
    .ant-card-body {
      padding: 16px;
    }
  }
  
  .cardContent {
    height: auto;
    min-height: 160px;
  }
  
  .cardHeader {
    margin-bottom: 12px;
  }
  
  .cardIcon {
    width: 36px;
    height: 36px;
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .pageHeader {
    padding: 12px;
  }
  
  .pageContent {
    padding: 12px;
  }
  
  .headerLeft h2 {
    font-size: 20px;
  }
  
  .knowledgeCard .ant-card-body {
    padding: 12px;
  }
  
  .cardContent {
    min-height: 140px;
  }
}
