import {
  useInfiniteFetchKnowledgeList,
  useCreateKnowledge,
  useDeleteKnowledge,
} from '@/hooks/knowledge-hooks';
import { useAuth } from '@/hooks/auth-hooks';
import AppLayout from '@/components/AppLayout';
import { formatDate } from '@/utils/date';
import { useTranslate } from '@/hooks/use-i18n';
import {
  PlusOutlined,
  SearchOutlined,
  BookOutlined,
  DeleteOutlined,
  EditOutlined,
  FileTextOutlined,
  MoreOutlined,
} from '@ant-design/icons';
import {
  Button,
  Card,
  Empty,
  Input,
  Space,
  Typography,
  Row,
  Col,
  Modal,
  Form,
  message,
  Dropdown,
  Tag,
  Statistic,
  Select,
} from 'antd';
import React, { useState, useMemo, useEffect } from 'react';
import InfiniteScroll from 'react-infinite-scroll-component';
import { useNavigate } from 'umi';
import styles from './index.less';

const { Title, Text } = Typography;
const { Search } = Input;
const { confirm } = Modal;

const KnowledgeListContent = () => {
  const t = useTranslate();
  const { userInfo } = useAuth();
  const navigate = useNavigate();
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [createForm] = Form.useForm();

  const {
    fetchNextPage,
    data,
    hasNextPage,
    searchString,
    handleInputChange,
    loading,
  } = useInfiniteFetchKnowledgeList();

  const { createKnowledge, loading: creatingLoading } = useCreateKnowledge();
  const { deleteKnowledge, loading: deletingLoading } = useDeleteKnowledge();

  const knowledgeList = useMemo(() => {
    const list =
      data?.pages?.flatMap((x) => (Array.isArray(x.kbs) ? x.kbs : [])) ?? [];
    return list;
  }, [data?.pages]);

  const total = useMemo(() => {
    return data?.pages.at(-1)?.total ?? 0;
  }, [data?.pages]);

  const handleCreateKnowledge = async () => {
    try {
      const values = await createForm.validateFields();
      console.log('Creating knowledge base with values:', values);

      const result = await createKnowledge({
        name: values.name,
        description: values.description || '',
        language: values.language || 'Chinese',
        permission: 'me',
      });

      console.log('Create knowledge base result:', result);

      setCreateModalVisible(false);
      createForm.resetFields();

      // 创建成功后直接跳转到Knowledge Base Settings页面
      // 检查不同可能的响应结构
      const kbId = result?.kb_id || result?.data?.kb_id || result?.id;
      console.log('Full result:', result);
      console.log('Extracted kb_id:', kbId);

      if (kbId) {
        message.success(t('knowledge.createSuccess', 'Knowledge base created successfully!'));
        console.log('Navigating to:', `/knowledge/${kbId}/setting`);
        // 使用setTimeout确保状态更新完成后再跳转
        setTimeout(() => {
          navigate(`/knowledge/${kbId}/setting`);
        }, 100);
      } else {
        console.warn('No kb_id found in response:', result);
        message.success(t('knowledge.createSuccess', 'Knowledge base created successfully!'));
        // 如果没有kb_id，刷新页面显示新创建的知识库
        setTimeout(() => {
          window.location.reload();
        }, 1000);
      }
    } catch (error) {
      console.error('Create knowledge base failed:', error);
      message.error(t('knowledge.createFailed', 'Failed to create knowledge base'));
    }
  };

  const handleDeleteKnowledge = (kb: any) => {
    confirm({
      title: t('knowledge.deleteTitle', 'Delete Knowledge Base'),
      content: t('knowledge.deleteConfirm', 'Are you sure you want to delete "{{name}}"? This action cannot be undone.', { name: kb.name }),
      okText: t('common.delete'),
      okType: 'danger',
      cancelText: t('common.cancel'),
      onOk: async () => {
        await deleteKnowledge(kb.id);
      },
    });
  };

  const getKnowledgeMenuItems = (kb: any) => [
    {
      key: 'view',
      icon: <FileTextOutlined />,
      label: t('knowledge.viewDocuments', 'View Documents'),
      onClick: () => {
        navigate(`/knowledge/${kb.id}/dataset`);
      },
    },
    {
      key: 'edit',
      icon: <EditOutlined />,
      label: t('common.edit'),
      onClick: () => {
        navigate(`/knowledge/${kb.id}/setting`);
      },
    },
    {
      key: 'delete',
      icon: <DeleteOutlined />,
      label: t('common.delete'),
      danger: true,
      onClick: () => handleDeleteKnowledge(kb),
    },
  ];

  return (
    <div className={styles.knowledgePage}>
      <div className={styles.pageHeader}>
        <div className={styles.headerContent}>
          <div className={styles.headerLeft}>
            <Title level={2} style={{ margin: 0 }}>
              {t('knowledge.title')}
            </Title>
            <Text type="secondary">
              {t('knowledge.manageDescription', 'Manage your knowledge repositories and documents')}
            </Text>
          </div>
          <div className={styles.headerRight}>
            <Space>
              <Search
                placeholder={t('knowledge.searchPlaceholder', 'Search knowledge bases...')}
                allowClear
                style={{ width: 300 }}
                value={searchString}
                onChange={(e) => handleInputChange(e.target.value)}
                loading={loading}
              />
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => setCreateModalVisible(true)}
                size="large"
              >
                {t('knowledge.createKnowledge')}
              </Button>
            </Space>
          </div>
        </div>
      </div>

      <div className={styles.pageContent}>
        {total > 0 && (
          <div className={styles.statsSection}>
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={8}>
                <Card>
                  <Statistic
                    title={t('knowledge.totalKnowledgeBases', 'Total Knowledge Bases')}
                    value={total}
                    prefix={<BookOutlined />}
                    valueStyle={{ color: '#1890ff' }}
                  />
                </Card>
              </Col>
              <Col xs={24} sm={8}>
                <Card>
                  <Statistic
                    title={t('knowledge.totalDocuments', 'Total Documents')}
                    value={knowledgeList.reduce((sum, kb) => sum + (kb.doc_num || 0), 0)}
                    prefix={<FileTextOutlined />}
                    valueStyle={{ color: '#52c41a' }}
                  />
                </Card>
              </Col>
              <Col xs={24} sm={8}>
                <Card>
                  <Statistic
                    title={t('knowledge.totalChunks', 'Total Chunks')}
                    value={knowledgeList.reduce((sum, kb) => sum + (kb.chunk_num || 0), 0)}
                    prefix={<SearchOutlined />}
                    valueStyle={{ color: '#722ed1' }}
                  />
                </Card>
              </Col>
            </Row>
          </div>
        )}

        <div className={styles.knowledgeGrid}>
          {knowledgeList.length === 0 && !loading ? (
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={
                <div>
                  <Text type="secondary">{t('knowledge.noKnowledgeBases', 'No knowledge bases found')}</Text>
                  <br />
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={() => setCreateModalVisible(true)}
                    style={{ marginTop: 16 }}
                  >
                    {t('knowledge.createFirstKnowledge', 'Create Your First Knowledge Base')}
                  </Button>
                </div>
              }
            />
          ) : (
            <InfiniteScroll
              dataLength={knowledgeList.length}
              next={fetchNextPage}
              hasMore={!!hasNextPage}
              loader={<div className={styles.loading}>{t('common.loading')}</div>}
              endMessage={
                knowledgeList.length > 0 && (
                  <div className={styles.endMessage}>
                    <Text type="secondary">{t('knowledge.noMoreKnowledgeBases', 'No more knowledge bases to load')}</Text>
                  </div>
                )
              }
            >
              <Row gutter={[24, 24]}>
                {knowledgeList.map((kb: any) => (
                  <Col xs={24} sm={12} lg={8} xl={6} key={kb.id}>
                    <Card
                      hoverable
                      className={styles.knowledgeCard}
                      onClick={() => navigate(`/knowledge/${kb.id}/dataset`)}
                      actions={[
                        <Button
                          type="text"
                          icon={<FileTextOutlined />}
                          onClick={(e) => {
                            e.stopPropagation();
                            navigate(`/knowledge/${kb.id}/dataset`);
                          }}
                        >
                          {t('knowledge.documents', 'Documents')}
                        </Button>,
                        <Dropdown
                          menu={{ items: getKnowledgeMenuItems(kb) }}
                          trigger={['click']}
                        >
                          <Button
                            type="text"
                            icon={<MoreOutlined />}
                            onClick={(e) => e.stopPropagation()}
                          />
                        </Dropdown>,
                      ]}
                    >
                      <div className={styles.cardContent}>
                        <div className={styles.cardHeader}>
                          <div className={styles.cardIcon}>
                            <BookOutlined />
                          </div>
                          <div className={styles.cardTitle}>
                            <Title level={5} ellipsis={{ tooltip: kb.name }}>
                              {kb.name}
                            </Title>
                            <Text type="secondary" className={styles.cardTime}>
                              {formatDate(kb.create_time, 'date')}
                            </Text>
                          </div>
                        </div>
                        
                        <div className={styles.cardDescription}>
                          <Text type="secondary" ellipsis={{ rows: 2, tooltip: kb.description }}>
                            {kb.description || t('knowledge.noDescription', 'No description provided')}
                          </Text>
                        </div>
                        
                        <div className={styles.cardStats}>
                          <Space split={<span className={styles.statsDivider}>|</span>}>
                            <span>
                              <FileTextOutlined /> {kb.doc_num || 0} {t('knowledge.docsCount', 'docs')}
                            </span>
                            <span>
                              <SearchOutlined /> {kb.chunk_num || 0} {t('knowledge.chunksCount', 'chunks')}
                            </span>
                          </Space>
                        </div>
                        
                        <div className={styles.cardTags}>
                          <Tag color="blue">{kb.language || t('languages.english')}</Tag>
                          {kb.permission === 'me' && <Tag color="green">{t('knowledge.permissionPrivate')}</Tag>}
                        </div>
                      </div>
                    </Card>
                  </Col>
                ))}
              </Row>
            </InfiniteScroll>
          )}
        </div>
      </div>

      <Modal
        title={t('knowledge.createKnowledge')}
        open={createModalVisible}
        onOk={handleCreateKnowledge}
        onCancel={() => {
          setCreateModalVisible(false);
          createForm.resetFields();
        }}
        confirmLoading={creatingLoading}
        width={600}
      >
        <Form
          form={createForm}
          layout="vertical"
          requiredMark={false}
        >
          <Form.Item
            name="name"
            label={t('knowledge.knowledgeName')}
            rules={[
              { required: true, message: t('knowledge.nameRequired') },
              { max: 100, message: t('messages.validation.maxLength', { max: 100 }) },
            ]}
          >
            <Input placeholder={t('knowledge.enterName')} />
          </Form.Item>
          
          <Form.Item
            name="description"
            label={t('knowledge.description')}
            rules={[
              { max: 500, message: t('messages.validation.maxLength', { max: 500 }) },
            ]}
          >
            <Input.TextArea
              rows={4}
              placeholder={t('knowledge.enterDescription')}
            />
          </Form.Item>
          
          <Form.Item
            name="language"
            label={t('common.language')}
            initialValue="Chinese"
          >
            <Select placeholder={t('knowledge.selectLanguage', 'Select language')}>
              <Select.Option value="English">{t('languages.english', 'English')}</Select.Option>
              <Select.Option value="Chinese">{t('languages.chinese', '中文')}</Select.Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

const KnowledgeList: React.FC = () => {
  return (
    <AppLayout showSearch={false}>
      <KnowledgeListContent />
    </AppLayout>
  );
};

export default KnowledgeList;
